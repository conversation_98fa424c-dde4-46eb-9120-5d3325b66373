/**
 * @description
 * 此文件主要用于配置基础的转发代理服务
 */
import proxy, { ITigerProxyOption } from '@tiger/proxy';
import { Middleware } from 'koa';
import Router from 'koa-router';
import { config } from '../config';

const router = new Router({
    prefix: config.contextPath
});

const fmsProxyOptions: ITigerProxyOption = {
    target: 'http://***************',
    changeOrigin: true,
    autoRewrite: true,
    headers: { Host: 'fms.you.163.com' },
    rewrite: (path: string) => {
        return path.replace(new RegExp(`^${config.contextPath}`), '');
    }
};
// 权限中心的转发配置
router.all('/xhr/userCenterManage/*', proxy('*', config.umcProxyOptions));

// 配置需要直接转发的应用模块 TODO
router.all('/xhr/proxy-module1/*', proxy('*', config.appProxyOptions));

router.all(['*/xhr/nos/*', '*/xhr/file/*'], async (ctx, next) => {
    const rst = await proxy('*', fmsProxyOptions)(ctx, next);
});
const middleware: Middleware = router.routes();
export default middleware;
