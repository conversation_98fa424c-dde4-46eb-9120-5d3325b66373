import {ITigerProxyOption} from '@tiger/proxy';
import BaseConfig from './config.base';
import {join} from 'path';

export default class Config extends BaseConfig {
    loggerPath: string = join('/home/<USER>/', this.serviceCode);
    // app自己的转发配置，需要开发自己改成自己应用的 TODO
    appProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-app.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path;
        }
    };

    umcProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/test.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
                ),
                ''
            );
        }
    };
}
