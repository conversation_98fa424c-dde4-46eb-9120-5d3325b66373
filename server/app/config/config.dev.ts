import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';

export default class Config extends BaseConfig {
    loggerPath: string = join(this.rootPath, 'log');
    appProxyOptions: ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path;
        }
    };
    umcProxyOptions: ITigerProxyOption = {
        target: 'http://***************',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'yxius.you.163.com' },
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
                ),
                ''
            );
        }
    };
    fmsProxyOptions: ITigerProxyOption = {
        target: 'http://***************',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'fms.you.163.com' },
        rewrite: (path: string) => {
            console.log('path', path);
            return path.replace(new RegExp(`^${this.contextPath}`), '');
        }
    };
}
