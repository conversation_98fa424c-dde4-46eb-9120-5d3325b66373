import { IAppConfig, IPlugin, NodeEnv } from '@tiger/core';
import { Configuration } from '@tiger/logger';
import { ITigerProxyOption } from '@tiger/proxy';
import { join } from 'path';
import { AppModuleConfig } from './types';

const rootPath = join(__dirname, '../');
const contextPath = '/oly-render';
const xhrPrefix = '/xhr';
const productCode = 'ops';
const serviceCode = 'oly-render';
const appName = serviceCode;

export default abstract class BaseConfig implements IAppConfig {
    // serviceCode
    serviceCode: string = serviceCode;
    // 应用的名字
    appName: string = appName;
    // contextPath，一般表示应用域名的后面一位，比如,yx.mail.netease.com/ape，contextPath就是ape
    contextPath: string = contextPath;
    // 一般将接口进行前缀区分，一般是/xhr
    xhrPrefix: string = xhrPrefix;
    // 应用研发工作台申请的productCode，申请地址：http://yx.mail.netease.com/ape/#/edit/project?from=new
    productCode: string = productCode;

    plugins: IPlugin[] = [];

    // 应用的根目录
    rootPath: string = rootPath;
    // 日志的目录
    abstract loggerPath: string;
    // 前端index.html,ico,fonts等资源目录
    webAppPath: string = join(rootPath, 'web/app');
    // 前端静态资源的存放目录
    webStaticPath: string = join(rootPath, 'web/mimg');
    // server监听端口
    port: number = 9300;
    // 环境
    env: string = NodeEnv.Current.env.code;

    // app自己的转发配置，需要开发自己改成自己应用的 TODO
    abstract appProxyOptions: ITigerProxyOption;
    // 权限中心的转发配置
    abstract umcProxyOptions: ITigerProxyOption;

    // 外部模块配置
    modules: AppModuleConfig = {
        '@tiger/security': {
            enable: true,
            options: {
                csrf: true,
                'Strict-Transport-Security': true,
                'X-Frame-Options': true
            }
        },
        '@tiger/swagger': {
            enable: true,
            options: {
                appModule: join(__dirname, '../modules/index.ts'),
                swaggerDefinition: {
                    host: 'local.yx.mail.netease.com'
                }
            }
        }
    };
}
