import {GetMapping, RequestMapping, RestController} from '@tiger/boot';
import {Context} from 'koa';
import send from 'koa-send';
import {config} from '../../config';

@RestController
@RequestMapping(`${config.contextPath}`)
export class StaticController {
    /**
     * 默认页面
     * @author: 金炳
     * @data: 2018-10-28 19:55:27
     */
    @GetMapping('/')
    public async defaultPage(ctx: Context) {
        await send(ctx, 'index.html', {
            root: config.webAppPath
        });
    }

    /**
     * 首页
     * @author: 金炳
     * @data: 2018-10-28 19:56:49
     */
    @GetMapping('/index.html')
    public async indexPage(ctx: Context) {
        await send(ctx, 'index.html', {
            root: config.webAppPath
        });
    }

    /**
     * 获取图片和css和js资源
     * @author: 金炳
     * @data: 2018-10-28 19:59:18
     */
    @GetMapping('/(css|img|js)/*')
    public async staticResource(ctx: Context) {
        const contextPathReg = new RegExp(`^${config.contextPath}/`);
        const reqPath = ctx.path.replace(contextPathReg, '');
        await send(ctx, reqPath, {
            root: config.webStaticPath
        });
    }

    /**
     * 获取图标
     * @author: 金炳
     * @data: 2018-10-28 19:59:34
     */
    @GetMapping('/favicon.ico')
    public async favicon(ctx: Context) {
        await send(ctx, 'favicon.ico', {
            root: config.webAppPath
        });
    }

    /**
     * 字体资源
     * @author: 金炳
     * @data: 2018-10-28 19:59:47
     */
    @GetMapping('/fonts/*')
    public async fonts(ctx: Context) {
        const contextPathReg = new RegExp(`^${config.contextPath}/`);
        const reqPath = ctx.path.replace(contextPathReg, '');
        await send(ctx, reqPath, {
            root: config.webAppPath
        });
    }

    /**
     * ejs渲染
     */
    @GetMapping('/ejs/index.html')
    public async ejsPage(ctx: Context) {
        await ctx.render('index');
    }
}
