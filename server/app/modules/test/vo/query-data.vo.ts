export class IUidVO {
    /**
     *  uid 即用户邮箱地址
     */
    uid: string;
}

export class IFileUploadOptionVO {
    /**
     * 主题， 强烈建议简单明了，方便以后统计组统计数据
     */
    topic: string;
    /**
     * 文件名 type =2时必须
     */
    fileName?: string;
    /**
     * 文件大小 type =2时必须
     */
    fileSize?: number;

    /**
     * 数据流，type为2时必需，可放入Request body中
     */
    inputStream?: Buffer;
    /**
     * 文件的url type=3时必须
     */
    url?: string;
    /**
     * 请求方式（即期望 fms 获取url文件的请求方式） type=3时可指定
     */
    getOrPost?: string;

}

export interface IFileUploadVO extends IFileUploadOptionVO {
    /**
     * 文件
     */
    file: File;
}

export interface IFilesUploadVO extends IFileUploadOptionVO {
    /**
     * 文件列表
     */
    files: Files;
}

export interface Files {
    [key: string]: File;
}

export interface File {
    size: number;
    path: string;
    name: string;
    type: string;
    lastModifiedDate?: Date;
    hash?: string;
    toJSON(): {};
}

export type IFileInfo = string[];
export type IFilesInfo = object[];

export class IFileDownloadOptionVO {
    /**
     * 主题， 强烈建议简单明了，方便以后统计组统计数据
     */
    topic: string;
    /**
     * 文件key
     */
    fileKey: string;
    // 文件 name
    fileName: string;

}

export interface IOrgPosUniteUserVO {
    orgId: number;
    orgPosId: number;
    orgPosName: string;
    userId: number;
    uid: string;
    type: number;
    employeeName: string;
    employeeNum: string;
    realName: string;
}

export interface IOrgPosUniteVO {
    level: number;
    orgPosId: number;
    orgPosName: string;
    parentOrgPosId: number;
    orgPosUniteUsers: IOrgPosUniteUserVO[];
}

export type IOrgListVO = IOrgPosUniteVO[];

export interface IUpperOrgPosMapVO {
    [key: number]: IOrgListVO;
}

export interface IUpperLealerInfoVO {
    leader: IOrgPosUniteUserVO[];
    instructor: IOrgPosUniteUserVO[];
}
