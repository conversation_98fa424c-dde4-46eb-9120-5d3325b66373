import { GetMapping, PostMapping, RequestMapping, RestController } from '@tiger/boot';
import { AjaxResult, QueryContext, FileContext } from '@tiger/core';
import { PermissionService, IUser, IFileInfo, FmsService, OrgService } from '@eagle/common-service-node';
import { IUidVO, IFileUploadVO, IFilesUploadVO, IFilesInfo, IFileDownloadOptionVO, IUpperLealerInfoVO, IUpperOrgPosMapVO } from './vo/query-data.vo';
import { Stream, PassThrough } from 'stream';

@RestController
@RequestMapping('/react-tempalte/xhr')
export class TestController {
    constructor(
        private permService: PermissionService,
        private fmsService: FmsService,
        private orgService: OrgService
    ) {
    }

    /**
     * @description
     * @param {QueryContext<null, AjaxResult<string>>} ctx
     * @memberof TestController
     */
    @GetMapping('/sayhello.do')
    public async sayhello(ctx: QueryContext<null, AjaxResult<string>>) {

        ctx.body = AjaxResult.success('hello');
    }
    /**
     * @description 根据用户id查询该用户所有的二级部门 负责人 及 指导员 信息
     * @param {*} ctx
     * @memberof TestController
     */
    @GetMapping('/getAllSecLevelLeadersByUid.do')
    public async getAllSecLevelLeadersByUid(ctx: QueryContext<IUidVO, AjaxResult<IUpperLealerInfoVO>>) {
        if (!ctx.query) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { uid } = ctx.query;
        const data = await this.orgService.getAllSecLevelLeadersByUid(uid);
        ctx.body = AjaxResult.success(data);
    }

    /**
     * @description  根据uid查询用户所处的所有上级组织架构数据
     * @param {*} ctx
     * @memberof TestController
     */
    @GetMapping('/getAllUpperInfo.do')
    public async getAllUpperInfo(ctx: QueryContext<IUidVO, AjaxResult<IUpperOrgPosMapVO>>) {
        if (!ctx.query) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { uid } = ctx.query;
        const data = await this.orgService.getAllUpperByOrgUid(uid);
        ctx.body = AjaxResult.success(data);
    }

    /**
     * @description 根据uid查询用户信息
     * @param {*} ctx
     * @memberof TestController
     */
    @GetMapping('/getUser.do')
    public async getUser(ctx: QueryContext<IUidVO, AjaxResult<IUser | null>>) {
        if (!ctx.query) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { uid } = ctx.query;
        try {
            const userInfo = await this.permService.getUser(uid);
            ctx.body = AjaxResult.success(userInfo);
        } catch (error) {
            console.error('查询用户信息失败:', error);
            ctx.body = AjaxResult.internal('查询用户失败');
        }
    }

    /**
     * @description 上传单个个文件
     * @param {FileContext<null, AjaxResult<string>, string[]>} ctx
     * @returns
     * @memberof TestController
     */
    @PostMapping('/uploadFile.do')
    public async uploadFile(ctx: FileContext<IFileUploadVO, AjaxResult<IFileInfo>, string[]>) {
        const files = ctx.request.files;
        if (!files) {
            return ctx.body = AjaxResult.badRequest('没有传文件');
        }
        if (!ctx.request.body) {
            return ctx.body = AjaxResult.badRequest('缺少参数');
        }
        const topic = ctx.request.body.topic;
        const file = files.file;
        const param: IFileUploadVO = { file, topic };
        // 调用文件上传服务
        const rst = await this.fmsService.uploadFile(param);
        ctx.body = rst;
    }

    /**
     * @description 上传多个文件
     * @param {FileContext<IFilesUploadVO, AjaxResult<string>, string[]>} ctx
     * @returns
     * @memberof TestController
     */
    @PostMapping('/uploadFiles.do')
    public async uploadFiles(ctx: FileContext<IFilesUploadVO, AjaxResult<IFilesInfo>, string[]>) {
        const files = ctx.request.files;
        if (!files) {
            return ctx.body = AjaxResult.badRequest('参数不全');
        }
        if (!ctx.request.body) {
            return ctx.body = AjaxResult.badRequest('缺少参数');
        }
        const topic = ctx.request.body.topic;
        const param: IFilesUploadVO = { files, topic: topic };
        // 调用文件上传服务
        const rst = await this.fmsService.uploadFiles(param);
        ctx.body = rst;
    }
    /**
     *
     * @description 下载文件
     * @param {(QueryContext<IFileDownloadOptionVO, Stream | AjaxResult<string>>)} ctx
     * @returns
     * @memberof TestController
     */
    @GetMapping('/downLoadFile.do')
    public async downLoadFile(ctx: QueryContext<IFileDownloadOptionVO, Stream | AjaxResult<string>>) {
        if (!ctx.query) {
            return ctx.body = AjaxResult.badRequest('参数错误');
        }
        const { topic, fileKey, fileName } = ctx.query;
        let _fileName;
        if (!fileName) {
            _fileName = fileKey;
        } else {
            _fileName = fileName;
        }
        const rst = await this.fmsService.downLoadFile({ topic, fileKey });
        ctx.response.set({
            'Content-Type': 'application/octet-stream;charset=utf-8'
/*             'Content-Disposition': `attachment; fileName=${fileName}`
 */        });
        ctx.response.attachment(_fileName);
        ctx.body = rst.on('error', ctx.onerror).pipe(new PassThrough());
    }
}
