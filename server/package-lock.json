{"name": "react-template-server", "version": "0.0.1", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.5.5", "resolved": "http://npm.mail.netease.com/registry/@babel/code-frame/download/@babel/code-frame-7.5.5.tgz", "integrity": "sha1-vAeC9tafe31JUxIZaZuYj2aaj50=", "requires": {"@babel/highlight": "^7.0.0"}}, "@babel/generator": {"version": "7.5.5", "resolved": "http://npm.mail.netease.com/registry/@babel/generator/download/@babel/generator-7.5.5.tgz", "integrity": "sha1-hzp/k2o8iUkbQ1NtEiRbYmZk488=", "dev": true, "requires": {"@babel/types": "^7.5.5", "jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "trim-right": "^1.0.1"}}, "@babel/helper-function-name": {"version": "7.1.0", "resolved": "http://npm.mail.netease.com/registry/@babel/helper-function-name/download/@babel/helper-function-name-7.1.0.tgz", "integrity": "sha1-oM6wFoX3M1XUNgwSR/WCv6/I/1M=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.0.0", "@babel/template": "^7.1.0", "@babel/types": "^7.0.0"}}, "@babel/helper-get-function-arity": {"version": "7.0.0", "resolved": "http://npm.mail.netease.com/registry/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0.tgz", "integrity": "sha1-g1ctQyDipGVyY3NBE8QoaLZOScM=", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@babel/helper-split-export-declaration": {"version": "7.4.4", "resolved": "http://npm.mail.netease.com/registry/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.4.4.tgz", "integrity": "sha1-/5SJSjQL549T8GrwOLIFxJ2ZNnc=", "dev": true, "requires": {"@babel/types": "^7.4.4"}}, "@babel/highlight": {"version": "7.5.0", "resolved": "http://npm.mail.netease.com/registry/@babel/highlight/download/@babel/highlight-7.5.0.tgz", "integrity": "sha1-VtETEr2SSPphlZHQJHK+boyzJUA=", "requires": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.5.5", "resolved": "http://npm.mail.netease.com/registry/@babel/parser/download/@babel/parser-7.5.5.tgz", "integrity": "sha1-AvB3rIgX099Kgy71neZ1Zeccyks=", "dev": true}, "@babel/template": {"version": "7.4.4", "resolved": "http://npm.mail.netease.com/registry/@babel/template/download/@babel/template-7.4.4.tgz", "integrity": "sha1-9LiNEiVomgj1vDoXSDVFvp5O0jc=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.4.4", "@babel/types": "^7.4.4"}}, "@babel/traverse": {"version": "7.5.5", "resolved": "http://npm.mail.netease.com/registry/@babel/traverse/download/@babel/traverse-7.5.5.tgz", "integrity": "sha1-9mT482jtMpiM1kjan3LVynDxZbs=", "dev": true, "requires": {"@babel/code-frame": "^7.5.5", "@babel/generator": "^7.5.5", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.4", "@babel/parser": "^7.5.5", "@babel/types": "^7.5.5", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.13"}}, "@babel/types": {"version": "7.5.5", "resolved": "http://npm.mail.netease.com/registry/@babel/types/download/@babel/types-7.5.5.tgz", "integrity": "sha1-l7n3KOGCeFkJqkq1YmTwkKAo0Yo=", "dev": true, "requires": {"esutils": "^2.0.2", "lodash": "^4.17.13", "to-fast-properties": "^2.0.0"}}, "@eagle/common-service-node": {"version": "3.0.3", "resolved": "http://npm.mail.netease.com/registry/@eagle/common-service-node/download/@eagle/common-service-node-3.0.3.tgz", "integrity": "sha1-6+MeQe29jx2AqgVHhmUWhIxuBjg=", "requires": {"@tiger/boot": "^3.0.0", "@tiger/core": "^3.0.0", "@tiger/openid": "^3.0.0", "@tiger/proxy": "^3.0.0", "@tiger/request": "^1.0.5", "@tiger/swagger": "3.0.6", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "debug": "^4.1.0", "request": "^2.88.0", "request-promise": "^4.2.2"}, "dependencies": {"@tiger/boot": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/download/@tiger/boot-3.0.1.tgz", "integrity": "sha1-dmrW5RGzc/Zr9QKif560QPkjEXo=", "requires": {"@tiger/error": "^3.0.0", "cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "@tiger/core": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/download/@tiger/core-3.0.2.tgz", "integrity": "sha1-ixx53GPOYhWuKgkRLfBNrafPRTE=", "requires": {"@tiger/tslint": "^3.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/error": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/download/@tiger/error-3.0.0.tgz", "integrity": "sha1-mYYuvQse9eptp+ouGp1B35krYZA=", "requires": {"@tiger/logger": "^3.0.0", "@types/boom": "^7.2.1", "@types/koa": "^2.0.46", "boom": "^7.2.2", "koa": "^2.6.1"}}, "@tiger/filter": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/download/@tiger/filter-3.0.0.tgz", "integrity": "sha1-dk5fVgiezmftgBguLdOi7of3g04=", "requires": {"@tiger/core": "^3.0.0", "path-to-regexp": "^1.7.0"}}, "@tiger/logger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/download/@tiger/logger-3.0.6.tgz", "integrity": "sha1-WLV7FfCWQ20ZYUlmg/ajHn7sQ2g=", "requires": {"@tiger/filter": "^3.0.0", "log4js": "^4.0.2"}}, "@tiger/openid": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/download/@tiger/openid-3.0.1.tgz", "integrity": "sha1-RG/whZYW9mrv44B3uWj6GSjY5JM=", "requires": {"@tiger/core": "^3.0.0", "@tiger/filter": "^3.0.0"}}, "@tiger/proxy": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/download/@tiger/proxy-3.0.0.tgz", "integrity": "sha1-o6AC83NebS2YD+NyNgwdouXQCXE=", "requires": {"@tiger/core": "^3.0.0", "@tiger/logger": "^3.0.0", "@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0", "path-to-regexp": "^1.7.0"}}, "@tiger/request": {"version": "1.0.5", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/download/@tiger/request-1.0.5.tgz", "integrity": "sha1-SZLfxqk0qE5tOddJO6xCgEpb5K4=", "requires": {"@tiger/core": "^1.0.1", "@tiger/logger": "^1.0.0", "axios": "^0.18.0"}, "dependencies": {"@tiger/core": {"version": "1.0.25", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/download/@tiger/core-1.0.25.tgz", "integrity": "sha1-0/tkAxu8JCbidJPOt7i5kf3jLnM=", "requires": {"@tiger/tslint": "^1.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/filter": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/download/@tiger/filter-1.0.2.tgz", "integrity": "sha1-N215eL2ACEtIJwpV2oZCl6SsxhU=", "requires": {"@tiger/core": "^1.0.1", "path-to-regexp": "^1.7.0"}}, "@tiger/logger": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/download/@tiger/logger-1.0.3.tgz", "integrity": "sha1-mUoM6udRh4LdI8iRbmkEwP0GUMY=", "requires": {"@tiger/core": "^1.0.1", "@tiger/filter": "^1.0.0", "log4js": "^3.0.6"}}, "@tiger/tslint": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/download/@tiger/tslint-1.0.2.tgz", "integrity": "sha1-oXELDrApdS0mUS3g0NsG/22cC4c=", "requires": {"tslint": "^5.12.0"}}, "debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "log4js": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/log4js/download/log4js-3.0.6.tgz", "integrity": "sha1-5srO2Uln7uuc45n5+GgqSysoyP8=", "requires": {"circular-json": "^0.5.5", "date-format": "^1.2.0", "debug": "^3.1.0", "rfdc": "^1.1.2", "streamroller": "0.7.0"}}}}, "@tiger/swagger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/download/@tiger/swagger-3.0.6.tgz", "integrity": "sha1-lGoReNAUGtZLPt0VtmG/c4amyMg=", "requires": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}}, "@tiger/tslint": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/download/@tiger/tslint-3.0.0.tgz", "integrity": "sha1-kP/yftI8eupyO4LbelJYwq2Bh94="}, "date-format": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/date-format/download/date-format-1.2.0.tgz", "integrity": "sha1-YV6CjiM90aubua4JUODOzPpuytg="}, "koa-send": {"version": "5.0.0", "resolved": "http://npm.mail.netease.com/registry/koa-send/download/koa-send-5.0.0.tgz", "integrity": "sha1-XoRB4H71VzdzTXztJbhC5QZG5+s=", "requires": {"debug": "^3.1.0", "http-errors": "^1.6.3", "mz": "^2.7.0", "resolve-path": "^1.4.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}, "streamroller": {"version": "0.7.0", "resolved": "http://npm.mail.netease.com/registry/streamroller/download/streamroller-0.7.0.tgz", "integrity": "sha1-odG3z4PTmvsNYwSaWsv5NJO99ks=", "requires": {"date-format": "^1.2.0", "debug": "^3.1.0", "mkdirp": "^0.5.1", "readable-stream": "^2.3.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}}}}}, "@eagle/workflow-node": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@eagle/workflow-node/download/@eagle/workflow-node-4.0.2.tgz", "integrity": "sha1-tYsggiTh5LCEHW3CoqqzMTXISlg="}, "@tiger/apolloy": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/apolloy/download/@tiger/apolloy-4.0.53.tgz", "integrity": "sha1-k+BBomJZmUGDwGKF+EsWvvO5BSU=", "requires": {"@tiger/logger": "^4.0.53", "reflect-metadata": "^0.1.12"}}, "@tiger/boot": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/download/@tiger/boot-4.0.53.tgz", "integrity": "sha1-DW6mqMJqj3giGO2l8rClcM5ipKo=", "requires": {"cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "@tiger/cache": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/cache/download/@tiger/cache-4.0.53.tgz", "integrity": "sha1-4iJkffwOcj0aDvq2gkDXuHjQdbI=", "requires": {"@types/debug": "^4.1.4", "@types/lru-cache": "^4.0.0", "debug": "^4.1.1", "lru-cache": "^4.0.0", "node-object-hash": "^1.4.2", "redis": "^2.8.0"}, "dependencies": {"@types/debug": {"version": "4.1.5", "resolved": "http://npm.mail.netease.com/registry/@types/debug/download/@types/debug-4.1.5.tgz", "integrity": "sha1-sU76iFK3do2JiQZhPCP2iHE+As0="}}}, "@tiger/core": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/download/@tiger/core-4.0.53.tgz", "integrity": "sha1-6S526U3M0LbAbKeYkUeOCfX+eQs=", "requires": {"@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/ejs": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/ejs/download/@tiger/ejs-4.0.53.tgz", "integrity": "sha1-UWiEWQBD+A8B9w3PUd52NS5f7Lg=", "requires": {"koa-ejs": "^4.1.2", "shimmer": "^1.2.0"}}, "@tiger/error": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/download/@tiger/error-4.0.53.tgz", "integrity": "sha1-v9UVvz9WtfWsHPw7q5kyfSb8K5Y=", "requires": {"@types/boom": "^7.2.1", "@types/koa": "^2.0.46", "boom": "^7.2.2", "koa": "^2.6.1"}}, "@tiger/filter": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/download/@tiger/filter-4.0.53.tgz", "integrity": "sha1-Q2U0GZU5p3OygBCARovvQ527+G8=", "requires": {"koa": "^2.6.1", "path-to-regexp": "^1.7.0"}}, "@tiger/health": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/health/download/@tiger/health-4.0.53.tgz", "integrity": "sha1-wsp2rmKKpHlPMEEWXyHJPWbyj1Y="}, "@tiger/info": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/info/download/@tiger/info-4.0.53.tgz", "integrity": "sha1-7A422DZJbWFV8N112AH6i6isHuE="}, "@tiger/logger": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/download/@tiger/logger-4.0.53.tgz", "integrity": "sha1-IfHZpHAmEdwXBSdgvM+DxdIsKS4=", "requires": {"log4js": "^4.0.2"}}, "@tiger/monitor": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/monitor/download/@tiger/monitor-4.0.53.tgz", "integrity": "sha1-X3rw5umGqm7a8neSfnlZaDFdmak=", "requires": {"axios": "^0.19.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13", "shimmer": "^1.2.1"}, "dependencies": {"axios": {"version": "0.19.0", "resolved": "http://npm.mail.netease.com/registry/axios/download/axios-0.19.0.tgz", "integrity": "sha1-jgm/89kSLhM/e4EByPvdAO09Krg=", "requires": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}}, "debug": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "http://npm.mail.netease.com/registry/follow-redirects/download/follow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "requires": {"debug": "=3.1.0"}}}}, "@tiger/openid": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/download/@tiger/openid-4.0.2.tgz", "integrity": "sha1-cyKjzLuOG72Ptpe8zgRoBGshvHM="}, "@tiger/permission": {"version": "4.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/permission/download/@tiger/permission-4.0.3.tgz", "integrity": "sha1-uU28KccYP5wml1CLAfsbQCs2D84=", "requires": {"@types/koa": "^2.0.46", "koa": "^2.5.1"}}, "@tiger/proxy": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/download/@tiger/proxy-4.0.53.tgz", "integrity": "sha1-QQmrdQa9sORCtZ0U/Hr/fd9S2l8=", "requires": {"@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0", "path-to-regexp": "^1.7.0"}}, "@tiger/request": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/download/@tiger/request-4.0.53.tgz", "integrity": "sha1-aM2wt0fdUSQU6NwJY8JoxUshg7Y=", "requires": {"axios": "^0.18.0"}}, "@tiger/security": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/security/download/@tiger/security-4.0.53.tgz", "integrity": "sha1-rUWqd8hE2PVgYQyyf3yiVhWEWDY="}, "@tiger/session": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/session/download/@tiger/session-4.0.53.tgz", "integrity": "sha1-TL7yAlKqficsWhmxZKBL3rh5ydQ=", "requires": {"@types/koa": "^2.0.45", "@types/redis": "^2.8.6", "koa": "^2.5.1", "lru-cache": "^4.1.1", "redis": "^2.8.0"}}, "@tiger/swagger": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/download/@tiger/swagger-4.0.53.tgz", "integrity": "sha1-Nvhr/voP3bAcd1qgLY0Z19Wsies=", "requires": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "koa-send": {"version": "5.0.0", "resolved": "http://npm.mail.netease.com/registry/koa-send/download/koa-send-5.0.0.tgz", "integrity": "sha1-XoRB4H71VzdzTXztJbhC5QZG5+s=", "requires": {"debug": "^3.1.0", "http-errors": "^1.6.3", "mz": "^2.7.0", "resolve-path": "^1.4.0"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "@tiger/tslint": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/download/@tiger/tslint-1.0.2.tgz", "integrity": "sha1-oXELDrApdS0mUS3g0NsG/22cC4c=", "dev": true, "requires": {"tslint": "^5.12.0"}}, "@tiger/validator": {"version": "4.0.53", "resolved": "http://npm.mail.netease.com/registry/@tiger/validator/download/@tiger/validator-4.0.53.tgz", "integrity": "sha1-rnfMnYgK2SIHN/OTOPReIC1B3XM=", "requires": {"class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13"}}, "@types/accepts": {"version": "1.3.5", "resolved": "http://npm.mail.netease.com/registry/@types/accepts/download/@types/accepts-1.3.5.tgz", "integrity": "sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/bluebird": {"version": "3.5.27", "resolved": "http://npm.mail.netease.com/registry/@types/bluebird/download/@types/bluebird-3.5.27.tgz", "integrity": "sha1-YetNddxr+85Rz0num76+lBsstdA="}, "@types/body-parser": {"version": "1.17.0", "resolved": "http://npm.mail.netease.com/registry/@types/body-parser/download/@types/body-parser-1.17.0.tgz", "integrity": "sha1-n1ydm9BLtUvjLV65/A2Ml05s9Yw=", "requires": {"@types/connect": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/boom": {"version": "7.3.0", "resolved": "http://npm.mail.netease.com/registry/@types/boom/download/@types/boom-7.3.0.tgz", "integrity": "sha1-MygMVVLUz6vCG4t+D20pKS3s2YU="}, "@types/caseless": {"version": "0.12.2", "resolved": "http://npm.mail.netease.com/registry/@types/caseless/download/@types/caseless-0.12.2.tgz", "integrity": "sha1-9l09Y4ngHutFi9VNyPUrlalGO8g="}, "@types/chai": {"version": "4.2.0", "resolved": "http://npm.mail.netease.com/registry/@types/chai/download/@types/chai-4.2.0.tgz", "integrity": "sha1-JHgmACFAjewywSOnytNBS+uBGgc=", "dev": true}, "@types/connect": {"version": "3.4.32", "resolved": "http://npm.mail.netease.com/registry/@types/connect/download/@types/connect-3.4.32.tgz", "integrity": "sha1-qg6WFrlDXMrQK8UrW0VP/Cxwuig=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/cookiejar": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/@types/cookiejar/download/@types/cookiejar-2.1.1.tgz", "integrity": "sha1-kLaERjZLr579joNJuza9OFK3W4A=", "dev": true}, "@types/cookies": {"version": "0.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/cookies/download/@types/cookies-0.7.2.tgz", "integrity": "sha1-XgVg1G7ZmYCC3OeZrxBY3WpJeAo=", "requires": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/debug": {"version": "4.1.7", "resolved": "https://registry.npmmirror.com/@types/debug/-/debug-4.1.7.tgz", "integrity": "sha512-9AonUzyTjXXhEOa0DnqpzZi6VHlqKMswga9EXjpXnnqxwLtdvPPtlO8evrI5D9S6asFRCQ6v+wpiUKbw+vKqyg==", "dev": true, "requires": {"@types/ms": "*"}}, "@types/ejs": {"version": "2.6.3", "resolved": "http://npm.mail.netease.com/registry/@types/ejs/download/@types/ejs-2.6.3.tgz", "integrity": "sha1-tlCemSXX616VyMc7ZJLluq58Hmo=", "dev": true}, "@types/etag": {"version": "1.8.0", "resolved": "http://npm.mail.netease.com/registry/@types/etag/download/@types/etag-1.8.0.tgz", "integrity": "sha1-N/Cx8+pG2nrjGbvttgfjdbTJn34=", "dev": true, "requires": {"@types/node": "*"}}, "@types/events": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@types/events/download/@types/events-3.0.0.tgz", "integrity": "sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc="}, "@types/express": {"version": "4.17.0", "resolved": "http://npm.mail.netease.com/registry/@types/express/download/@types/express-4.17.0.tgz", "integrity": "sha1-SertsglYKobxLtm3JRYPEtBO8oc=", "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.16.7", "resolved": "http://npm.mail.netease.com/registry/@types/express-serve-static-core/download/@types/express-serve-static-core-4.16.7.tgz", "integrity": "sha1-ULpvimkcCKPdn6f7ol7zEz0pgEk=", "requires": {"@types/node": "*", "@types/range-parser": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/formidable": {"version": "1.0.31", "resolved": "http://npm.mail.netease.com/registry/@types/formidable/download/@types/formidable-1.0.31.tgz", "integrity": "sha1-J0+dwtChqc4f7vSMJMoIWefslHs=", "requires": {"@types/events": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/fs-extra": {"version": "5.1.0", "resolved": "http://npm.mail.netease.com/registry/@types/fs-extra/download/@types/fs-extra-5.1.0.tgz", "integrity": "sha1-KjJe+XkBUEo4KHGMOQ00uEJqEKE=", "dev": true, "requires": {"@types/node": "*"}}, "@types/glob": {"version": "7.1.1", "resolved": "http://npm.mail.netease.com/registry/@types/glob/download/@types/glob-7.1.1.tgz", "integrity": "sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=", "dev": true, "requires": {"@types/events": "*", "@types/minimatch": "*", "@types/node": "*"}}, "@types/handlebars": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/@types/handlebars/download/@types/handlebars-4.1.0.tgz", "integrity": "sha1-P8zpv4j4X+c9yTIkCrP7aCxiSFA=", "dev": true, "requires": {"handlebars": "*"}}, "@types/highlight.js": {"version": "9.12.3", "resolved": "http://npm.mail.netease.com/registry/@types/highlight.js/download/@types/highlight.js-9.12.3.tgz", "integrity": "sha1-tnLPqsJcu8Y0oP2SxRX2b6oY28o=", "dev": true}, "@types/http-assert": {"version": "1.5.0", "resolved": "http://npm.mail.netease.com/registry/@types/http-assert/download/@types/http-assert-1.5.0.tgz", "integrity": "sha1-VslcabUecWiw1nJwBdH7KgCq75Q="}, "@types/http-proxy": {"version": "1.17.0", "resolved": "http://npm.mail.netease.com/registry/@types/http-proxy/download/@types/http-proxy-1.17.0.tgz", "integrity": "sha1-uvgv9qonI/0p+Q47oThOZlAGhj4=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/jasmine": {"version": "2.8.16", "resolved": "http://npm.mail.netease.com/registry/@types/jasmine/download/@types/jasmine-2.8.16.tgz", "integrity": "sha1-pssksRSdZSk71haSNQABSDjhTn0=", "dev": true}, "@types/joi": {"version": "14.3.3", "resolved": "http://npm.mail.netease.com/registry/@types/joi/download/@types/joi-14.3.3.tgz", "integrity": "sha1-8lGqgVD8C2p86f6rIYAqKEc94zU=", "dev": true}, "@types/keygrip": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/@types/keygrip/download/@types/keygrip-1.0.1.tgz", "integrity": "sha1-/1QEYtL7TQqIRBzq8n0oewHD2Hg="}, "@types/koa": {"version": "2.0.49", "resolved": "http://npm.mail.netease.com/registry/@types/koa/download/@types/koa-2.0.49.tgz", "integrity": "sha1-j/wt291xWiw5KiGMZ+EWywcAcjQ=", "requires": {"@types/accepts": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/koa-compose": {"version": "3.2.4", "resolved": "http://npm.mail.netease.com/registry/@types/koa-compose/download/@types/koa-compose-3.2.4.tgz", "integrity": "sha1-dqRhY0pZw+E0SYMXCLubNV+xVI4=", "requires": {"@types/koa": "*"}}, "@types/koa-etag": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@types/koa-etag/download/@types/koa-etag-3.0.0.tgz", "integrity": "sha1-0U09q0XVV3uUvHKWBjHelnUTQdM=", "dev": true, "requires": {"@types/etag": "*", "@types/koa": "*"}}, "@types/koa-router": {"version": "7.0.35", "resolved": "http://npm.mail.netease.com/registry/@types/koa-router/download/@types/koa-router-7.0.35.tgz", "integrity": "sha1-B0QqyV5l7BsEKvT3Kr6kTclrkJ8=", "dev": true, "requires": {"@types/koa": "*"}}, "@types/koa-send": {"version": "4.1.2", "resolved": "http://npm.mail.netease.com/registry/@types/koa-send/download/@types/koa-send-4.1.2.tgz", "integrity": "sha1-l4+CZ60RbRKsahj+zY80xWV+Ca0=", "dev": true, "requires": {"@types/koa": "*"}}, "@types/lodash": {"version": "4.14.136", "resolved": "http://npm.mail.netease.com/registry/@types/lodash/download/@types/lodash-4.14.136.tgz", "integrity": "sha1-QT6FCJBGuGXZYMn/HUAOBMMatg8=", "dev": true}, "@types/lru-cache": {"version": "4.1.2", "resolved": "http://npm.mail.netease.com/registry/@types/lru-cache/download/@types/lru-cache-4.1.2.tgz", "integrity": "sha1-UoujkmWAVdunj8O+kGyjOKGi0cU="}, "@types/marked": {"version": "0.4.2", "resolved": "http://npm.mail.netease.com/registry/@types/marked/download/@types/marked-0.4.2.tgz", "integrity": "sha1-ZKieU+o39hzA8+4XMsVVwtv2RS8=", "dev": true}, "@types/mime": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/@types/mime/download/@types/mime-2.0.1.tgz", "integrity": "sha1-3EiIQjEqfwdRSTEpBbXjwLBUx50="}, "@types/minimatch": {"version": "3.0.3", "resolved": "http://npm.mail.netease.com/registry/@types/minimatch/download/@types/minimatch-3.0.3.tgz", "integrity": "sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=", "dev": true}, "@types/ms": {"version": "0.7.31", "resolved": "https://registry.npmmirror.com/@types/ms/-/ms-0.7.31.tgz", "integrity": "sha512-iiUgKzV9AuaEkZqkOLDIvlQiL6ltuZd9tGcW3gwpnX8JbuiuhFlEGmmFXEXkN50Cvq7Os88IY2v0dkDqXYWVgA==", "dev": true}, "@types/node": {"version": "8.10.51", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-8.10.51.tgz", "integrity": "sha1-gGAIV8Ckeo6Lr8La5trtbbWONic=", "dev": true}, "@types/range-parser": {"version": "1.2.3", "resolved": "http://npm.mail.netease.com/registry/@types/range-parser/download/@types/range-parser-1.2.3.tgz", "integrity": "sha1-fuMwunyq+5gJC+zoal7kQRWQTCw="}, "@types/redis": {"version": "2.8.13", "resolved": "http://npm.mail.netease.com/registry/@types/redis/download/@types/redis-2.8.13.tgz", "integrity": "sha1-/ddtmot8Nv8k2FBqlO8qiQyH9Jg=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/request": {"version": "2.48.2", "resolved": "http://npm.mail.netease.com/registry/@types/request/download/@types/request-2.48.2.tgz", "integrity": "sha1-k2N0y+EXnX7VKfwCVD3rRZdFD+0=", "requires": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.0"}, "dependencies": {"@types/node": {"version": "12.7.2", "resolved": "http://npm.mail.netease.com/registry/@types/node/download/@types/node-12.7.2.tgz", "integrity": "sha1-xOY69eiCPOnMPws097mYwhcfDEQ="}}}, "@types/request-promise": {"version": "4.1.44", "resolved": "http://npm.mail.netease.com/registry/@types/request-promise/download/@types/request-promise-4.1.44.tgz", "integrity": "sha1-BbWc0YRFgy+uFraNW7PUYhtUlIU=", "requires": {"@types/bluebird": "*", "@types/request": "*"}}, "@types/serve-static": {"version": "1.13.2", "resolved": "http://npm.mail.netease.com/registry/@types/serve-static/download/@types/serve-static-1.13.2.tgz", "integrity": "sha1-9axNemQgqZpqRa9HGfTc2M2Qekg=", "requires": {"@types/express-serve-static-core": "*", "@types/mime": "*"}}, "@types/shelljs": {"version": "0.8.5", "resolved": "http://npm.mail.netease.com/registry/@types/shelljs/download/@types/shelljs-0.8.5.tgz", "integrity": "sha1-HlB7L20fiTJpvT6FHsJEGe+b7uo=", "dev": true, "requires": {"@types/glob": "*", "@types/node": "*"}}, "@types/superagent": {"version": "4.1.3", "resolved": "http://npm.mail.netease.com/registry/@types/superagent/download/@types/superagent-4.1.3.tgz", "integrity": "sha1-UlZt3YlXJztHfD4YfJMP15GpWy4=", "dev": true, "requires": {"@types/cookiejar": "*", "@types/node": "*"}}, "@types/supertest": {"version": "2.0.8", "resolved": "http://npm.mail.netease.com/registry/@types/supertest/download/@types/supertest-2.0.8.tgz", "integrity": "sha1-I4ASNuK4UgTtdxqOfED+u6faK9o=", "dev": true, "requires": {"@types/superagent": "*"}}, "@types/tough-cookie": {"version": "2.3.5", "resolved": "http://npm.mail.netease.com/registry/@types/tough-cookie/download/@types/tough-cookie-2.3.5.tgz", "integrity": "sha1-naRO11VxmZtlw3tgybK4jbVMWF0="}, "@vscode-snippets/tiger": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/@vscode-snippets/tiger/download/@vscode-snippets/tiger-1.0.0.tgz", "integrity": "sha1-5y/rBjmBDcPYMYuzQgqbRM77VjY=", "dev": true}, "abbrev": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true}, "accepts": {"version": "1.3.7", "resolved": "http://npm.mail.netease.com/registry/accepts/download/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "ajv": {"version": "6.10.2", "resolved": "http://npm.mail.netease.com/registry/ajv/download/ajv-6.10.2.tgz", "integrity": "sha1-086gTWsBeyiUrWkED+yLYj60vVI=", "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-align": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/ansi-align/download/ansi-align-2.0.0.tgz", "integrity": "sha1-w2rsy6VjuJzrVW82kPCx2eNUf38=", "dev": true, "requires": {"string-width": "^2.0.0"}}, "ansi-regex": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/ansi-regex/download/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="}, "ansi-styles": {"version": "3.2.1", "resolved": "http://npm.mail.netease.com/registry/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "any-promise": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8="}, "anymatch": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/anymatch/download/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/normalize-path/download/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "argparse": {"version": "1.0.10", "resolved": "http://npm.mail.netease.com/registry/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/arr-diff/download/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/arr-flatten/download/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/arr-union/download/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "http://npm.mail.netease.com/registry/array-unique/download/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "arrify": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/arrify/download/arrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asn1": {"version": "0.2.4", "resolved": "http://npm.mail.netease.com/registry/asn1/download/asn1-0.2.4.tgz", "integrity": "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU="}, "assertion-error": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/assertion-error/download/assertion-error-1.1.0.tgz", "integrity": "sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs=", "dev": true}, "assign-symbols": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/assign-symbols/download/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "async": {"version": "2.6.3", "resolved": "http://npm.mail.netease.com/registry/async/download/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "requires": {"lodash": "^4.17.14"}}, "async-each": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/async-each/download/async-each-1.0.3.tgz", "integrity": "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=", "dev": true}, "asynckit": {"version": "0.4.0", "resolved": "http://npm.mail.netease.com/registry/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "atob": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/atob/download/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true}, "aws-sign2": {"version": "0.7.0", "resolved": "http://npm.mail.netease.com/registry/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg="}, "aws4": {"version": "1.8.0", "resolved": "http://npm.mail.netease.com/registry/aws4/download/aws4-1.8.0.tgz", "integrity": "sha1-8OAD2cqef1nHpQiUXXsu+aBKVC8="}, "axios": {"version": "0.18.1", "resolved": "http://npm.mail.netease.com/registry/axios/download/axios-0.18.1.tgz", "integrity": "sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=", "requires": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "http://npm.mail.netease.com/registry/follow-redirects/download/follow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "requires": {"debug": "=3.1.0"}}}}, "balanced-match": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "base": {"version": "0.11.2", "resolved": "http://npm.mail.netease.com/registry/base/download/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "requires": {"tweetnacl": "^0.14.3"}}, "binary-extensions": {"version": "1.13.1", "resolved": "http://npm.mail.netease.com/registry/binary-extensions/download/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=", "dev": true}, "bluebird": {"version": "3.5.5", "resolved": "http://npm.mail.netease.com/registry/bluebird/download/bluebird-3.5.5.tgz", "integrity": "sha1-qNCv1zJR7/u9X+OEp31zADwXpx8="}, "boom": {"version": "7.3.0", "resolved": "http://npm.mail.netease.com/registry/boom/download/boom-7.3.0.tgz", "integrity": "sha1-czptlW0zsLGZnaP+bBKZaVDQF7k=", "requires": {"hoek": "6.x.x"}}, "boxen": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/boxen/download/boxen-1.3.0.tgz", "integrity": "sha1-VcbDmouljZxhrSLNh3Uy3rZlogs=", "dev": true, "requires": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/camelcase/download/camelcase-4.1.0.tgz", "integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "dev": true}}}, "brace-expansion": {"version": "1.1.11", "resolved": "http://npm.mail.netease.com/registry/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "http://npm.mail.netease.com/registry/braces/download/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "buffer-from": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/buffer-from/download/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "builtin-modules": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/builtin-modules/download/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="}, "bytes": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/bytes/download/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "cache-base": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/cache-base/download/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "cache-content-type": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/cache-content-type/download/cache-content-type-1.0.1.tgz", "integrity": "sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=", "requires": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}}, "call-me-maybe": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/call-me-maybe/download/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms="}, "camelcase": {"version": "5.3.1", "resolved": "http://npm.mail.netease.com/registry/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="}, "capture-stack-trace": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/capture-stack-trace/download/capture-stack-trace-1.0.1.tgz", "integrity": "sha1-psC74fOPOqC5Ijjstv9Cw0TUE10=", "dev": true}, "caseless": {"version": "0.12.0", "resolved": "http://npm.mail.netease.com/registry/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw="}, "chai": {"version": "4.2.0", "resolved": "http://npm.mail.netease.com/registry/chai/download/chai-4.2.0.tgz", "integrity": "sha1-dgqnLPION5XoSxKHfODoNzeqKeU=", "dev": true, "requires": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^3.0.1", "get-func-name": "^2.0.0", "pathval": "^1.1.0", "type-detect": "^4.0.5"}}, "chalk": {"version": "2.4.2", "resolved": "http://npm.mail.netease.com/registry/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "check-error": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/check-error/download/check-error-1.0.2.tgz", "integrity": "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII=", "dev": true}, "chokidar": {"version": "2.1.6", "resolved": "http://npm.mail.netease.com/registry/chokidar/download/chokidar-2.1.6.tgz", "integrity": "sha1-tsrWU6kp4kTOioNCRBZNJB+pVMU=", "dev": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "ci-info": {"version": "1.6.0", "resolved": "http://npm.mail.netease.com/registry/ci-info/download/ci-info-1.6.0.tgz", "integrity": "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=", "dev": true}, "circular-json": {"version": "0.5.9", "resolved": "http://npm.mail.netease.com/registry/circular-json/download/circular-json-0.5.9.tgz", "integrity": "sha1-kydjroj0996teg0JyKUaR0OlOx0="}, "class-transformer": {"version": "0.2.3", "resolved": "http://npm.mail.netease.com/registry/class-transformer/download/class-transformer-0.2.3.tgz", "integrity": "sha1-WYySynHcynP5HMuHXXSjhHzPoy0="}, "class-utils": {"version": "0.3.6", "resolved": "http://npm.mail.netease.com/registry/class-utils/download/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "class-validator": {"version": "0.9.1", "resolved": "http://npm.mail.netease.com/registry/class-validator/download/class-validator-0.9.1.tgz", "integrity": "sha1-1g5YxdFKvKCkG844z3kq1MRtFTE=", "requires": {"google-libphonenumber": "^3.1.6", "validator": "10.4.0"}, "dependencies": {"validator": {"version": "10.4.0", "resolved": "http://npm.mail.netease.com/registry/validator/download/validator-10.4.0.tgz", "integrity": "sha1-7pmkSvs7te01ChWfBWynKiBM/Dw="}}}, "cli-boxes": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/cli-boxes/download/cli-boxes-1.0.0.tgz", "integrity": "sha1-T6kXw+WclKAEzWH47lCdplFocUM=", "dev": true}, "cliui": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/cliui/download/cliui-4.1.0.tgz", "integrity": "sha1-NIQi2+gtgAswIu709qwQvy5NG0k=", "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "co": {"version": "4.6.0", "resolved": "http://npm.mail.netease.com/registry/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "co-body": {"version": "5.2.0", "resolved": "http://npm.mail.netease.com/registry/co-body/download/co-body-5.2.0.tgz", "integrity": "sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=", "requires": {"inflation": "^2.0.0", "qs": "^6.4.0", "raw-body": "^2.2.0", "type-is": "^1.6.14"}}, "code-point-at": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/code-point-at/download/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="}, "collection-visit": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/collection-visit/download/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://npm.mail.netease.com/registry/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://npm.mail.netease.com/registry/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "combined-stream": {"version": "1.0.8", "resolved": "http://npm.mail.netease.com/registry/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.0", "resolved": "http://npm.mail.netease.com/registry/commander/download/commander-2.20.0.tgz", "integrity": "sha1-1YuytcHuj4ew00ACfp6U4iLFpCI="}, "component-emitter": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/component-emitter/download/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "http://npm.mail.netease.com/registry/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "configstore": {"version": "3.1.2", "resolved": "http://npm.mail.netease.com/registry/configstore/download/configstore-3.1.2.tgz", "integrity": "sha1-xvJd767vJt8S3TNBSwAf6BpUP48=", "dev": true, "requires": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}}, "content-disposition": {"version": "0.5.3", "resolved": "http://npm.mail.netease.com/registry/content-disposition/download/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "http://npm.mail.netease.com/registry/content-type/download/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "cookiejar": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/cookiejar/download/cookiejar-2.1.2.tgz", "integrity": "sha1-3YojVTB1L5iPmghE8/xYnjERElw=", "dev": true}, "cookies": {"version": "0.7.3", "resolved": "http://npm.mail.netease.com/registry/cookies/download/cookies-0.7.3.tgz", "integrity": "sha1-eRLOIfvy6MLacM8cPzUa7PWdrfo=", "requires": {"depd": "~1.1.2", "keygrip": "~1.0.3"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "core-js": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/core-js/download/core-js-2.6.9.tgz", "integrity": "sha1-a0shRiDINBUuF5Mjcn/Bl0GwhPI="}, "core-util-is": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "create-error-class": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/create-error-class/download/create-error-class-3.0.2.tgz", "integrity": "sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=", "dev": true, "requires": {"capture-stack-trace": "^1.0.0"}}, "cron": {"version": "1.7.1", "resolved": "http://npm.mail.netease.com/registry/cron/download/cron-1.7.1.tgz", "integrity": "sha1-6F7p33lNG8ZXmJbuOCBTw84zd48=", "requires": {"moment-timezone": "^0.5.x"}}, "cross-spawn": {"version": "6.0.5", "resolved": "http://npm.mail.netease.com/registry/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "crypto-random-string": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/crypto-random-string/download/crypto-random-string-1.0.0.tgz", "integrity": "sha1-ojD2T1aDEOFJgAmUB5DsmVRbyn4=", "dev": true}, "dashdash": {"version": "1.14.1", "resolved": "http://npm.mail.netease.com/registry/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "requires": {"assert-plus": "^1.0.0"}}, "date-format": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/date-format/download/date-format-2.1.0.tgz", "integrity": "sha1-MdW16iEc9f12TNOLr50DPffhJc8="}, "debug": {"version": "4.1.1", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-4.1.1.tgz", "integrity": "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=", "requires": {"ms": "^2.1.1"}, "dependencies": {"ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "decamelize": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/decamelize/download/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://npm.mail.netease.com/registry/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-eql": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/deep-eql/download/deep-eql-3.0.1.tgz", "integrity": "sha1-38lARACtHI/gI+faHfHBR8S0RN8=", "dev": true, "requires": {"type-detect": "^4.0.0"}}, "deep-equal": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/deep-equal/download/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "deep-extend": {"version": "0.6.0", "resolved": "http://npm.mail.netease.com/registry/deep-extend/download/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "dev": true}, "define-property": {"version": "2.0.2", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "delegates": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/delegates/download/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="}, "depd": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "destroy": {"version": "1.0.4", "resolved": "http://npm.mail.netease.com/registry/destroy/download/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "diff": {"version": "3.5.0", "resolved": "http://npm.mail.netease.com/registry/diff/download/diff-3.5.0.tgz", "integrity": "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI="}, "dot-prop": {"version": "4.2.0", "resolved": "http://npm.mail.netease.com/registry/dot-prop/download/dot-prop-4.2.0.tgz", "integrity": "sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc=", "dev": true, "requires": {"is-obj": "^1.0.0"}}, "double-ended-queue": {"version": "2.1.0-0", "resolved": "http://npm.mail.netease.com/registry/double-ended-queue/download/double-ended-queue-2.1.0-0.tgz", "integrity": "sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw="}, "duplexer3": {"version": "0.1.4", "resolved": "http://npm.mail.netease.com/registry/duplexer3/download/duplexer3-0.1.4.tgz", "integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=", "dev": true}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://npm.mail.netease.com/registry/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "ee-first": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "ejs": {"version": "2.6.2", "resolved": "http://npm.mail.netease.com/registry/ejs/download/ejs-2.6.2.tgz", "integrity": "sha1-OjLGPRzRbREmbNRwOxT+xOdKtPY="}, "end-of-stream": {"version": "1.4.1", "resolved": "http://npm.mail.netease.com/registry/end-of-stream/download/end-of-stream-1.4.1.tgz", "integrity": "sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=", "requires": {"once": "^1.4.0"}}, "error-ex": {"version": "1.3.2", "resolved": "http://npm.mail.netease.com/registry/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "error-inject": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/error-inject/download/error-inject-1.0.0.tgz", "integrity": "sha1-4rPZG1Su1nLzCdlQ0VSFD6EdTzc="}, "escape-html": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://npm.mail.netease.com/registry/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "esprima": {"version": "4.0.1", "resolved": "http://npm.mail.netease.com/registry/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="}, "esutils": {"version": "2.0.3", "resolved": "http://npm.mail.netease.com/registry/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="}, "etag": {"version": "1.8.1", "resolved": "http://npm.mail.netease.com/registry/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "3.1.2", "resolved": "http://npm.mail.netease.com/registry/eventemitter3/download/eventemitter3-3.1.2.tgz", "integrity": "sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc="}, "execa": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "expand-brackets": {"version": "2.1.4", "resolved": "http://npm.mail.netease.com/registry/expand-brackets/download/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "extend": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="}, "extend-shallow": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "http://npm.mail.netease.com/registry/extglob/download/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU="}, "fast-deep-equal": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk="}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I="}, "fill-range": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/fill-range/download/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-up": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "requires": {"locate-path": "^3.0.0"}}, "flatted": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/flatted/download/flatted-2.0.1.tgz", "integrity": "sha1-aeV8qo8OrLwoHS4stFjUb9tEngg="}, "follow-redirects": {"version": "1.7.0", "resolved": "http://npm.mail.netease.com/registry/follow-redirects/download/follow-redirects-1.7.0.tgz", "integrity": "sha1-SJ68GY3A5/ZBZ70jsDxMGbV4THY=", "requires": {"debug": "^3.2.6"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "for-in": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/for-in/download/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "forever-agent": {"version": "0.6.1", "resolved": "http://npm.mail.netease.com/registry/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE="}, "form-data": {"version": "2.5.0", "resolved": "http://npm.mail.netease.com/registry/form-data/download/form-data-2.5.0.tgz", "integrity": "sha1-CU7DWdxLVefWLg20rNduif6HTTc=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "format-util": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/format-util/download/format-util-1.0.3.tgz", "integrity": "sha1-Ay3KShFiYqEsQ/TD7IVmQWxbLZU="}, "formidable": {"version": "1.2.1", "resolved": "http://npm.mail.netease.com/registry/formidable/download/formidable-1.2.1.tgz", "integrity": "sha1-cPt8oCkO5v+WEJBBX0s989IIJlk="}, "fragment-cache": {"version": "0.2.1", "resolved": "http://npm.mail.netease.com/registry/fragment-cache/download/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fresh": {"version": "0.5.2", "resolved": "http://npm.mail.netease.com/registry/fresh/download/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs-extra": {"version": "7.0.1", "resolved": "http://npm.mail.netease.com/registry/fs-extra/download/fs-extra-7.0.1.tgz", "integrity": "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=", "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "1.2.9", "resolved": "http://npm.mail.netease.com/registry/fsevents/download/fsevents-1.2.9.tgz", "integrity": "sha1-P17WZYPM1vQAtaANtvfoYTY+OI8=", "dev": true, "optional": true, "requires": {"nan": "^2.12.1", "node-pre-gyp": "^0.12.0"}, "dependencies": {"abbrev": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true, "optional": true}, "aproba": {"version": "1.2.0", "bundled": true, "dev": true, "optional": true}, "are-we-there-yet": {"version": "1.1.5", "bundled": true, "dev": true, "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "optional": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "chownr": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true, "optional": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true, "optional": true}, "console-control-strings": {"version": "1.1.0", "bundled": true, "dev": true, "optional": true}, "core-util-is": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "debug": {"version": "4.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"ms": "^2.1.1"}}, "deep-extend": {"version": "0.6.0", "bundled": true, "dev": true, "optional": true}, "delegates": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "detect-libc": {"version": "1.0.3", "bundled": true, "dev": true, "optional": true}, "fs-minipass": {"version": "1.2.5", "bundled": true, "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "gauge": {"version": "2.7.4", "bundled": true, "dev": true, "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "glob": {"version": "7.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "has-unicode": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "iconv-lite": {"version": "0.4.24", "bundled": true, "dev": true, "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-walk": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"minimatch": "^3.0.4"}}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "optional": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true, "optional": true}, "ini": {"version": "1.3.5", "bundled": true, "dev": true, "optional": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true, "requires": {"number-is-nan": "^1.0.0"}}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true, "optional": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "optional": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true, "optional": true}, "minipass": {"version": "2.3.5", "bundled": true, "dev": true, "optional": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "minizlib": {"version": "1.2.1", "bundled": true, "dev": true, "optional": true, "requires": {"minipass": "^2.2.1"}}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "optional": true, "requires": {"minimist": "0.0.8"}}, "ms": {"version": "2.1.1", "bundled": true, "dev": true, "optional": true}, "needle": {"version": "2.3.0", "bundled": true, "dev": true, "optional": true, "requires": {"debug": "^4.1.0", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}}, "node-pre-gyp": {"version": "0.12.0", "bundled": true, "dev": true, "optional": true, "requires": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.1", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.2.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}}, "nopt": {"version": "4.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "npm-bundled": {"version": "1.0.6", "bundled": true, "dev": true, "optional": true}, "npm-packlist": {"version": "1.4.1", "bundled": true, "dev": true, "optional": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "npmlog": {"version": "4.1.2", "bundled": true, "dev": true, "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true, "optional": true}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "optional": true, "requires": {"wrappy": "1"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "osenv": {"version": "0.1.5", "bundled": true, "dev": true, "optional": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true, "optional": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "rc": {"version": "1.2.8", "bundled": true, "dev": true, "optional": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "dev": true, "optional": true}}}, "readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "optional": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "rimraf": {"version": "2.6.3", "bundled": true, "dev": true, "optional": true, "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.1.2", "bundled": true, "dev": true, "optional": true}, "safer-buffer": {"version": "2.1.2", "bundled": true, "dev": true, "optional": true}, "sax": {"version": "1.2.4", "bundled": true, "dev": true, "optional": true}, "semver": {"version": "5.7.0", "bundled": true, "dev": true, "optional": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true, "optional": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true, "optional": true}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "optional": true, "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "optional": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "dev": true, "optional": true}, "tar": {"version": "4.4.8", "bundled": true, "dev": true, "optional": true, "requires": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}}, "util-deprecate": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "wide-align": {"version": "1.1.3", "bundled": true, "dev": true, "optional": true, "requires": {"string-width": "^1.0.2 || 2"}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true}, "yallist": {"version": "3.0.3", "bundled": true, "dev": true, "optional": true}}}, "get-caller-file": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/get-caller-file/download/get-caller-file-1.0.3.tgz", "integrity": "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o="}, "get-func-name": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/get-func-name/download/get-func-name-2.0.0.tgz", "integrity": "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE=", "dev": true}, "get-port": {"version": "3.2.0", "resolved": "http://npm.mail.netease.com/registry/get-port/download/get-port-3.2.0.tgz", "integrity": "sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=", "dev": true}, "get-stream": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "requires": {"pump": "^3.0.0"}}, "get-value": {"version": "2.0.6", "resolved": "http://npm.mail.netease.com/registry/get-value/download/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "http://npm.mail.netease.com/registry/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.1.4", "resolved": "http://npm.mail.netease.com/registry/glob/download/glob-7.1.4.tgz", "integrity": "sha1-qmCKL2xXetNX4a5aXCbZqNGWklU=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/glob-parent/download/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/is-glob/download/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "requires": {"is-extglob": "^2.1.0"}}}}, "global-dirs": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/global-dirs/download/global-dirs-0.1.1.tgz", "integrity": "sha1-sxnA3UYH81PzvpzKTHL8FIxJ9EU=", "dev": true, "requires": {"ini": "^1.3.4"}}, "globals": {"version": "11.12.0", "resolved": "http://npm.mail.netease.com/registry/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "google-libphonenumber": {"version": "3.2.3", "resolved": "http://npm.mail.netease.com/registry/google-libphonenumber/download/google-libphonenumber-3.2.3.tgz", "integrity": "sha1-XlwTjIG4c0675e01T2zdIuG1PpU="}, "got": {"version": "6.7.1", "resolved": "http://npm.mail.netease.com/registry/got/download/got-6.7.1.tgz", "integrity": "sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=", "dev": true, "requires": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "dependencies": {"get-stream": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true}}}, "graceful-fs": {"version": "4.2.2", "resolved": "http://npm.mail.netease.com/registry/graceful-fs/download/graceful-fs-4.2.2.tgz", "integrity": "sha1-bwlSYF0BQMHP2xOO0AV3W5LWewI="}, "handlebars": {"version": "4.1.2", "resolved": "http://npm.mail.netease.com/registry/handlebars/download/handlebars-4.1.2.tgz", "integrity": "sha1-trN8HO0DBrIh4JT8eso+wjsTG2c=", "dev": true, "requires": {"neo-async": "^2.6.0", "optimist": "^0.6.1", "source-map": "^0.6.1", "uglify-js": "^3.1.4"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://npm.mail.netease.com/registry/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "har-schema": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI="}, "har-validator": {"version": "5.1.3", "resolved": "http://npm.mail.netease.com/registry/har-validator/download/har-validator-5.1.3.tgz", "integrity": "sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=", "requires": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}}, "has-flag": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-value": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/has-value/download/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/has-values/download/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "highlight.js": {"version": "9.15.9", "resolved": "http://npm.mail.netease.com/registry/highlight.js/download/highlight.js-9.15.9.tgz", "integrity": "sha1-hlJX2h27SljEVS1GxLOFT3fw5tU=", "dev": true}, "hoek": {"version": "6.1.3", "resolved": "http://npm.mail.netease.com/registry/hoek/download/hoek-6.1.3.tgz", "integrity": "sha1-c7fTOVLgH+J6OLBFcpS3ndjaJCw="}, "hosted-git-info": {"version": "2.8.4", "resolved": "http://npm.mail.netease.com/registry/hosted-git-info/download/hosted-git-info-2.8.4.tgz", "integrity": "sha1-RBGauvS8ZGkqFqzjRwD+2cA+JUY=", "dev": true}, "http-assert": {"version": "1.4.1", "resolved": "http://npm.mail.netease.com/registry/http-assert/download/http-assert-1.4.1.tgz", "integrity": "sha1-xfcl1neqfoc+9zYZm4lobM6zeHg=", "requires": {"deep-equal": "~1.0.1", "http-errors": "~1.7.2"}}, "http-errors": {"version": "1.7.3", "resolved": "http://npm.mail.netease.com/registry/http-errors/download/http-errors-1.7.3.tgz", "integrity": "sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=", "requires": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "http-proxy": {"version": "1.17.0", "resolved": "http://npm.mail.netease.com/registry/http-proxy/download/http-proxy-1.17.0.tgz", "integrity": "sha1-etOElGWPhGBeL220Q230EPTlvpo=", "requires": {"eventemitter3": "^3.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-signature": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://npm.mail.netease.com/registry/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-by-default": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/ignore-by-default/download/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk=", "dev": true}, "import-lazy": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/import-lazy/download/import-lazy-2.1.0.tgz", "integrity": "sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "http://npm.mail.netease.com/registry/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "inflation": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/inflation/download/inflation-2.0.0.tgz", "integrity": "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8="}, "inflight": {"version": "1.0.6", "resolved": "http://npm.mail.netease.com/registry/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://npm.mail.netease.com/registry/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "ini": {"version": "1.3.5", "resolved": "http://npm.mail.netease.com/registry/ini/download/ini-1.3.5.tgz", "integrity": "sha1-7uJfVtscnsYIXgwid4CD9Zar+Sc=", "dev": true}, "install": {"version": "0.12.2", "resolved": "http://npm.mail.netease.com/registry/install/download/install-0.12.2.tgz", "integrity": "sha1-6hDpssu1sEhPJQgLJLWzRCnZ9WQ=", "dev": true}, "interpret": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/interpret/download/interpret-1.2.0.tgz", "integrity": "sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY=", "dev": true}, "invert-kv": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/invert-kv/download/invert-kv-2.0.0.tgz", "integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI="}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://npm.mail.netease.com/registry/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "http://npm.mail.netease.com/registry/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-binary-path": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/is-binary-path/download/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "2.0.3", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-2.0.3.tgz", "integrity": "sha1-Ts8/z3ScvR5HJonhCaxmJhol5yU="}, "is-ci": {"version": "1.2.1", "resolved": "http://npm.mail.netease.com/registry/is-ci/download/is-ci-1.2.1.tgz", "integrity": "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=", "dev": true, "requires": {"ci-info": "^1.5.0"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://npm.mail.netease.com/registry/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "resolved": "http://npm.mail.netease.com/registry/is-descriptor/download/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true}}}, "is-extendable": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/is-extendable/download/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "is-generator-function": {"version": "1.0.7", "resolved": "http://npm.mail.netease.com/registry/is-generator-function/download/is-generator-function-1.0.7.tgz", "integrity": "sha1-0hMuUpuwAAp/gHlNS99c1eWBNSI="}, "is-glob": {"version": "4.0.1", "resolved": "http://npm.mail.netease.com/registry/is-glob/download/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-installed-globally": {"version": "0.1.0", "resolved": "http://npm.mail.netease.com/registry/is-installed-globally/download/is-installed-globally-0.1.0.tgz", "integrity": "sha1-Df2Y9akRFxbdU13aZJL2e/PSWoA=", "dev": true, "requires": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}}, "is-npm": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-npm/download/is-npm-1.0.0.tgz", "integrity": "sha1-8vtjpl5JBbQGyGBydloaTceTufQ=", "dev": true}, "is-number": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/is-number/download/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-obj": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/is-obj/download/is-obj-1.0.1.tgz", "integrity": "sha1-PkcprB9f3gJc19g6iW2rn09n2w8=", "dev": true}, "is-path-inside": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/is-path-inside/download/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://npm.mail.netease.com/registry/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-redirect": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-redirect/download/is-redirect-1.0.0.tgz", "integrity": "sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=", "dev": true}, "is-retry-allowed": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/is-retry-allowed/download/is-retry-allowed-1.1.0.tgz", "integrity": "sha1-EaBgVotnM5REAz0BJaYaINVk+zQ=", "dev": true}, "is-stream": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="}, "is-typedarray": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-utf8": {"version": "0.2.1", "resolved": "http://npm.mail.netease.com/registry/is-utf8/download/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/is-windows/download/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "http://npm.mail.netease.com/registry/isarray/download/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "isemail": {"version": "3.2.0", "resolved": "http://npm.mail.netease.com/registry/isemail/download/isemail-3.2.0.tgz", "integrity": "sha1-WTEKAhkxqfsGu7UeFVzgs/I2gyw=", "requires": {"punycode": "2.x.x"}}, "isexe": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "isstream": {"version": "0.1.2", "resolved": "http://npm.mail.netease.com/registry/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo="}, "istanbul-lib-coverage": {"version": "2.0.5", "resolved": "http://npm.mail.netease.com/registry/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz", "integrity": "sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k=", "dev": true}, "istanbul-lib-instrument": {"version": "3.3.0", "resolved": "http://npm.mail.netease.com/registry/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz", "integrity": "sha1-pfY9kfC7wMPkee9MXeAnM17G1jA=", "dev": true, "requires": {"@babel/generator": "^7.4.0", "@babel/parser": "^7.4.3", "@babel/template": "^7.4.0", "@babel/traverse": "^7.4.3", "@babel/types": "^7.4.0", "istanbul-lib-coverage": "^2.0.5", "semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://npm.mail.netease.com/registry/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "jasmine": {"version": "3.4.0", "resolved": "http://npm.mail.netease.com/registry/jasmine/download/jasmine-3.4.0.tgz", "integrity": "sha1-D6aJA/8MlpdFnNBEtE9NzvXsi9w=", "dev": true, "requires": {"glob": "^7.1.3", "jasmine-core": "~3.4.0"}}, "jasmine-core": {"version": "3.4.0", "resolved": "http://npm.mail.netease.com/registry/jasmine-core/download/jasmine-core-3.4.0.tgz", "integrity": "sha1-KnRhjpZgJlMMNRjwPp+EXSZHPOM=", "dev": true}, "jasmine-ts": {"version": "0.3.0", "resolved": "http://npm.mail.netease.com/registry/jasmine-ts/download/jasmine-ts-0.3.0.tgz", "integrity": "sha1-1gS0YkzxHi7dp1u9cC2/s+DfM6s=", "dev": true, "requires": {"yargs": "^8.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "camelcase": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/camelcase/download/camelcase-4.1.0.tgz", "integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "dev": true}, "cliui": {"version": "3.2.0", "resolved": "http://npm.mail.netease.com/registry/cliui/download/cliui-3.2.0.tgz", "integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}, "dependencies": {"string-width": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/string-width/download/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}}}, "cross-spawn": {"version": "5.1.0", "resolved": "http://npm.mail.netease.com/registry/cross-spawn/download/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "0.7.0", "resolved": "http://npm.mail.netease.com/registry/execa/download/execa-0.7.0.tgz", "integrity": "sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=", "dev": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true}, "invert-kv": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/invert-kv/download/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "lcid": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/lcid/download/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "mem": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/mem/download/mem-1.1.0.tgz", "integrity": "sha1-Xt1StIXKHZAP5kiVUFOZoN+kX3Y=", "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true}, "os-locale": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/os-locale/download/os-locale-2.1.0.tgz", "integrity": "sha1-QrwpAKa1uL0XN2yOiCtlr8zyS/I=", "dev": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "y18n": {"version": "3.2.1", "resolved": "http://npm.mail.netease.com/registry/y18n/download/y18n-3.2.1.tgz", "integrity": "sha1-bRX7qITAhnnA136I53WegR4H+kE=", "dev": true}, "yargs": {"version": "8.0.2", "resolved": "http://npm.mail.netease.com/registry/yargs/download/yargs-8.0.2.tgz", "integrity": "sha1-YpmpBVsc78lp/355wdkY3Osiw2A=", "dev": true, "requires": {"camelcase": "^4.1.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "read-pkg-up": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^7.0.0"}}, "yargs-parser": {"version": "7.0.0", "resolved": "http://npm.mail.netease.com/registry/yargs-parser/download/yargs-parser-7.0.0.tgz", "integrity": "sha1-jQrELxbqVd69MyyvTEA4s+P139k=", "dev": true, "requires": {"camelcase": "^4.1.0"}}}}, "joi": {"version": "14.3.1", "resolved": "http://npm.mail.netease.com/registry/joi/download/joi-14.3.1.tgz", "integrity": "sha1-FkomLsC4VUZuDDXuoqiFrotscDw=", "requires": {"hoek": "6.x.x", "isemail": "3.x.x", "topo": "3.x.x"}}, "js-tokens": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "3.13.1", "resolved": "http://npm.mail.netease.com/registry/js-yaml/download/js-yaml-3.13.1.tgz", "integrity": "sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM="}, "jsesc": {"version": "2.5.2", "resolved": "http://npm.mail.netease.com/registry/jsesc/download/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "http://npm.mail.netease.com/registry/json-schema/download/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "json-schema-ref-parser": {"version": "5.1.3", "resolved": "http://npm.mail.netease.com/registry/json-schema-ref-parser/download/json-schema-ref-parser-5.1.3.tgz", "integrity": "sha1-+GxYaPQImOaRaeG7yFRyWk/Q4a0=", "requires": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "js-yaml": "^3.12.0", "ono": "^4.0.6"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://npm.mail.netease.com/registry/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="}, "json-stable-stringify": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "requires": {"jsonify": "~0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://npm.mail.netease.com/registry/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus="}, "jsonfile": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/jsonfile/download/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "jsonify": {"version": "0.0.0", "resolved": "http://npm.mail.netease.com/registry/jsonify/download/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="}, "jsonschema": {"version": "1.2.4", "resolved": "http://npm.mail.netease.com/registry/jsonschema/download/jsonschema-1.2.4.tgz", "integrity": "sha1-pGusXTUGolRGW8VIh24mfG0NZGQ="}, "jsonschema-draft4": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/jsonschema-draft4/download/jsonschema-draft4-1.0.0.tgz", "integrity": "sha1-8K8gBQVPDwrefqIRhhS2ncUS2GU="}, "jsprim": {"version": "1.4.1", "resolved": "http://npm.mail.netease.com/registry/jsprim/download/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "keygrip": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/keygrip/download/keygrip-1.0.3.tgz", "integrity": "sha1-OZ1wnwrtK6sKBZ4M3TpQI6BT4dw="}, "kind-of": {"version": "6.0.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-6.0.2.tgz", "integrity": "sha1-ARRrNqYhjmTljzqNZt5df8b20FE=", "dev": true}, "koa": {"version": "2.7.0", "resolved": "http://npm.mail.netease.com/registry/koa/download/koa-2.7.0.tgz", "integrity": "sha1-fgCENQaUK52CxswzdJ9lfG5eet8=", "requires": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.7.1", "debug": "~3.1.0", "delegates": "^1.0.0", "depd": "^1.1.2", "destroy": "^1.0.4", "error-inject": "^1.0.0", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^1.2.0", "koa-is-json": "^1.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}}}, "koa-body": {"version": "4.1.1", "resolved": "http://npm.mail.netease.com/registry/koa-body/download/koa-body-4.1.1.tgz", "integrity": "sha1-UGhtKQiR/G8ay5hs98/NYF+FXvA=", "requires": {"@types/formidable": "^1.0.31", "co-body": "^5.1.1", "formidable": "^1.1.1"}}, "koa-compose": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/koa-compose/download/koa-compose-4.1.0.tgz", "integrity": "sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc="}, "koa-convert": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/koa-convert/download/koa-convert-1.2.0.tgz", "integrity": "sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=", "requires": {"co": "^4.6.0", "koa-compose": "^3.0.0"}, "dependencies": {"koa-compose": {"version": "3.2.1", "resolved": "http://npm.mail.netease.com/registry/koa-compose/download/koa-compose-3.2.1.tgz", "integrity": "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=", "requires": {"any-promise": "^1.1.0"}}}}, "koa-ejs": {"version": "4.2.0", "resolved": "http://npm.mail.netease.com/registry/koa-ejs/download/koa-ejs-4.2.0.tgz", "integrity": "sha1-N1w4dZQR6CjPVFQakI2XaKPqZfQ=", "requires": {"debug": "^2.6.1", "ejs": "^2.6.1", "mz": "^2.6.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}}}, "koa-etag": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/koa-etag/download/koa-etag-3.0.0.tgz", "integrity": "sha1-nvc4Ld1agqsN6xU0FckVg293HT8=", "requires": {"etag": "^1.3.0", "mz": "^2.1.0"}}, "koa-is-json": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/koa-is-json/download/koa-is-json-1.0.0.tgz", "integrity": "sha1-JzwH7c3Ljfaiwat9We52SRRR7BQ="}, "koa-router": {"version": "7.4.0", "resolved": "http://npm.mail.netease.com/registry/koa-router/download/koa-router-7.4.0.tgz", "integrity": "sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=", "requires": {"debug": "^3.1.0", "http-errors": "^1.3.1", "koa-compose": "^3.0.0", "methods": "^1.0.1", "path-to-regexp": "^1.1.1", "urijs": "^1.19.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "koa-compose": {"version": "3.2.1", "resolved": "http://npm.mail.netease.com/registry/koa-compose/download/koa-compose-3.2.1.tgz", "integrity": "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=", "requires": {"any-promise": "^1.1.0"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "koa-send": {"version": "4.1.3", "resolved": "http://npm.mail.netease.com/registry/koa-send/download/koa-send-4.1.3.tgz", "integrity": "sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=", "requires": {"debug": "^2.6.3", "http-errors": "^1.6.1", "mz": "^2.6.0", "resolve-path": "^1.4.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}}}, "latest-version": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/latest-version/download/latest-version-3.1.0.tgz", "integrity": "sha1-ogU4P+oyKzO1rjsYq+4NwvNW7hU=", "dev": true, "requires": {"package-json": "^4.0.0"}}, "lcid": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/lcid/download/lcid-2.0.0.tgz", "integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "requires": {"invert-kv": "^2.0.0"}}, "load-json-file": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/load-json-file/download/load-json-file-2.0.0.tgz", "integrity": "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.15", "resolved": "http://npm.mail.netease.com/registry/lodash/download/lodash-4.17.15.tgz", "integrity": "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="}, "lodash.get": {"version": "4.4.2", "resolved": "http://npm.mail.netease.com/registry/lodash.get/download/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk="}, "lodash.isequal": {"version": "4.5.0", "resolved": "http://npm.mail.netease.com/registry/lodash.isequal/download/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA="}, "log4js": {"version": "4.5.1", "resolved": "http://npm.mail.netease.com/registry/log4js/download/log4js-4.5.1.tgz", "integrity": "sha1-5UNiXpfZ5vPm58n8GW3WqyyuMLU=", "requires": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.4", "streamroller": "^1.0.6"}}, "lowercase-keys": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/lowercase-keys/download/lowercase-keys-1.0.1.tgz", "integrity": "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=", "dev": true}, "lru-cache": {"version": "4.1.5", "resolved": "http://npm.mail.netease.com/registry/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-dir": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/make-dir/download/make-dir-1.3.0.tgz", "integrity": "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "make-error": {"version": "1.3.5", "resolved": "http://npm.mail.netease.com/registry/make-error/download/make-error-1.3.5.tgz", "integrity": "sha1-7+ToH22yjK3WBccPKcgxtY73dsg=", "dev": true}, "map-age-cleaner": {"version": "0.1.3", "resolved": "http://npm.mail.netease.com/registry/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz", "integrity": "sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=", "requires": {"p-defer": "^1.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "http://npm.mail.netease.com/registry/map-cache/download/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/map-visit/download/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "marked": {"version": "0.4.0", "resolved": "http://npm.mail.netease.com/registry/marked/download/marked-0.4.0.tgz", "integrity": "sha1-mtLCp6F5HxCoUuARL3e1cdzhDGY=", "dev": true}, "media-typer": {"version": "0.3.0", "resolved": "http://npm.mail.netease.com/registry/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "mem": {"version": "4.3.0", "resolved": "http://npm.mail.netease.com/registry/mem/download/mem-4.3.0.tgz", "integrity": "sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=", "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}}, "methods": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "3.1.10", "resolved": "http://npm.mail.netease.com/registry/micromatch/download/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "mime": {"version": "1.6.0", "resolved": "http://npm.mail.netease.com/registry/mime/download/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true}, "mime-db": {"version": "1.40.0", "resolved": "http://npm.mail.netease.com/registry/mime-db/download/mime-db-1.40.0.tgz", "integrity": "sha1-plBX6ZjbCQ9zKmj2wnbTh9QSbDI="}, "mime-types": {"version": "2.1.24", "resolved": "http://npm.mail.netease.com/registry/mime-types/download/mime-types-2.1.24.tgz", "integrity": "sha1-tvjQs+lR77d97eyhlM/20W9nb4E=", "requires": {"mime-db": "1.40.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "minimatch": {"version": "3.0.4", "resolved": "http://npm.mail.netease.com/registry/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "resolved": "http://npm.mail.netease.com/registry/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}, "mixin-deep": {"version": "1.3.2", "resolved": "http://npm.mail.netease.com/registry/mixin-deep/download/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.1", "resolved": "http://npm.mail.netease.com/registry/mkdirp/download/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}}, "moment": {"version": "2.24.0", "resolved": "http://npm.mail.netease.com/registry/moment/download/moment-2.24.0.tgz", "integrity": "sha1-DQVdU/UFKqZTyfbraLtdEr9cK1s="}, "moment-timezone": {"version": "0.5.26", "resolved": "http://npm.mail.netease.com/registry/moment-timezone/download/moment-timezone-0.5.26.tgz", "integrity": "sha1-wCZ8oJroRjGqPcM/Zb7b5ujg13I=", "requires": {"moment": ">= 2.9.0"}}, "ms": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "mz": {"version": "2.7.0", "resolved": "http://npm.mail.netease.com/registry/mz/download/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "requires": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "nan": {"version": "2.14.0", "resolved": "http://npm.mail.netease.com/registry/nan/download/nan-2.14.0.tgz", "integrity": "sha1-eBj3IgJ7JFmobwKV1DTR/CM2xSw=", "dev": true, "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://npm.mail.netease.com/registry/nanomatch/download/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "negotiator": {"version": "0.6.2", "resolved": "http://npm.mail.netease.com/registry/negotiator/download/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "neo-async": {"version": "2.6.1", "resolved": "http://npm.mail.netease.com/registry/neo-async/download/neo-async-2.6.1.tgz", "integrity": "sha1-rCetpmFn+ohJpq3dg39rGJrSCBw=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://npm.mail.netease.com/registry/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="}, "node-object-hash": {"version": "1.4.2", "resolved": "http://npm.mail.netease.com/registry/node-object-hash/download/node-object-hash-1.4.2.tgz", "integrity": "sha1-OFgz2FsimQK3WCYiT2B3vpaanpQ="}, "nodemon": {"version": "1.19.1", "resolved": "http://npm.mail.netease.com/registry/nodemon/download/nodemon-1.19.1.tgz", "integrity": "sha1-V28KrQ+GOqv4xIUX9hkv+YfNUHE=", "dev": true, "requires": {"chokidar": "^2.1.5", "debug": "^3.1.0", "ignore-by-default": "^1.0.1", "minimatch": "^3.0.4", "pstree.remy": "^1.1.6", "semver": "^5.5.0", "supports-color": "^5.2.0", "touch": "^3.1.0", "undefsafe": "^2.0.2", "update-notifier": "^2.5.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "nopt": {"version": "1.0.10", "resolved": "http://npm.mail.netease.com/registry/nopt/download/nopt-1.0.10.tgz", "integrity": "sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=", "dev": true, "requires": {"abbrev": "1"}}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://npm.mail.netease.com/registry/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true}, "npm": {"version": "6.10.3", "resolved": "http://npm.mail.netease.com/registry/npm/download/npm-6.10.3.tgz", "integrity": "sha1-gxlVmPiTCkDuSAVniDhjMhYmZm4=", "dev": true, "requires": {"JSONStream": "^1.3.5", "abbrev": "~1.1.1", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "aproba": "^2.0.0", "archy": "~1.0.0", "bin-links": "^1.1.2", "bluebird": "^3.5.5", "byte-size": "^5.0.1", "cacache": "^12.0.2", "call-limit": "^1.1.1", "chownr": "^1.1.2", "ci-info": "^2.0.0", "cli-columns": "^3.1.2", "cli-table3": "^0.5.1", "cmd-shim": "~2.0.2", "columnify": "~1.5.4", "config-chain": "^1.1.12", "debuglog": "*", "detect-indent": "~5.0.0", "detect-newline": "^2.1.0", "dezalgo": "~1.0.3", "editor": "~1.0.0", "figgy-pudding": "^3.5.1", "find-npm-prefix": "^1.0.2", "fs-vacuum": "~1.2.10", "fs-write-stream-atomic": "~1.0.10", "gentle-fs": "^2.0.1", "glob": "^7.1.4", "graceful-fs": "^4.2.0", "has-unicode": "~2.0.1", "hosted-git-info": "^2.8.2", "iferr": "^1.0.2", "imurmurhash": "*", "infer-owner": "^1.0.4", "inflight": "~1.0.6", "inherits": "^2.0.4", "ini": "^1.3.5", "init-package-json": "^1.10.3", "is-cidr": "^3.0.0", "json-parse-better-errors": "^1.0.2", "lazy-property": "~1.0.0", "libcipm": "^4.0.0", "libnpm": "^3.0.1", "libnpmaccess": "^3.0.2", "libnpmhook": "^5.0.3", "libnpmorg": "^1.0.1", "libnpmsearch": "^2.0.2", "libnpmteam": "^1.0.2", "libnpx": "^10.2.0", "lock-verify": "^2.1.0", "lockfile": "^1.0.4", "lodash._baseindexof": "*", "lodash._baseuniq": "~4.6.0", "lodash._bindcallback": "*", "lodash._cacheindexof": "*", "lodash._createcache": "*", "lodash._getnative": "*", "lodash.clonedeep": "~4.5.0", "lodash.restparam": "*", "lodash.union": "~4.6.0", "lodash.uniq": "~4.5.0", "lodash.without": "~4.4.0", "lru-cache": "^5.1.1", "meant": "~1.0.1", "mississippi": "^3.0.0", "mkdirp": "~0.5.1", "move-concurrently": "^1.0.1", "node-gyp": "^5.0.3", "nopt": "~4.0.1", "normalize-package-data": "^2.5.0", "npm-audit-report": "^1.3.2", "npm-cache-filename": "~1.0.2", "npm-install-checks": "~3.0.0", "npm-lifecycle": "^3.1.2", "npm-package-arg": "^6.1.0", "npm-packlist": "^1.4.4", "npm-pick-manifest": "^2.2.3", "npm-profile": "^4.0.2", "npm-registry-fetch": "^4.0.0", "npm-user-validate": "~1.0.0", "npmlog": "~4.1.2", "once": "~1.4.0", "opener": "^1.5.1", "osenv": "^0.1.5", "pacote": "^9.5.4", "path-is-inside": "~1.0.2", "promise-inflight": "~1.0.1", "qrcode-terminal": "^0.12.0", "query-string": "^6.8.2", "qw": "~1.0.1", "read": "~1.0.7", "read-cmd-shim": "~1.0.1", "read-installed": "~4.0.3", "read-package-json": "^2.0.13", "read-package-tree": "^5.3.1", "readable-stream": "^3.4.0", "readdir-scoped-modules": "^1.1.0", "request": "^2.88.0", "retry": "^0.12.0", "rimraf": "^2.6.3", "safe-buffer": "^5.1.2", "semver": "^5.7.0", "sha": "^3.0.0", "slide": "~1.1.6", "sorted-object": "~2.0.1", "sorted-union-stream": "~2.1.3", "ssri": "^6.0.1", "stringify-package": "^1.0.0", "tar": "^4.4.10", "text-table": "~0.2.0", "tiny-relative-date": "^1.3.0", "uid-number": "0.0.6", "umask": "~1.1.0", "unique-filename": "^1.1.1", "unpipe": "~1.0.0", "update-notifier": "^2.5.0", "uuid": "^3.3.2", "validate-npm-package-license": "^3.0.4", "validate-npm-package-name": "~3.0.0", "which": "^1.3.1", "worker-farm": "^1.7.0", "write-file-atomic": "^2.4.3"}, "dependencies": {"JSONStream": {"version": "1.3.5", "bundled": true, "dev": true, "requires": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}}, "abbrev": {"version": "1.1.1", "bundled": true, "dev": true}, "agent-base": {"version": "4.3.0", "bundled": true, "dev": true, "requires": {"es6-promisify": "^5.0.0"}}, "agentkeepalive": {"version": "3.5.2", "bundled": true, "dev": true, "requires": {"humanize-ms": "^1.2.1"}}, "ajv": {"version": "5.5.2", "bundled": true, "dev": true, "requires": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.3.0"}}, "ansi-align": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.0.0"}}, "ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "ansi-styles": {"version": "3.2.1", "bundled": true, "dev": true, "requires": {"color-convert": "^1.9.0"}}, "ansicolors": {"version": "0.3.2", "bundled": true, "dev": true}, "ansistyles": {"version": "0.1.3", "bundled": true, "dev": true}, "aproba": {"version": "2.0.0", "bundled": true, "dev": true}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "are-we-there-yet": {"version": "1.1.4", "bundled": true, "dev": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "asap": {"version": "2.0.6", "bundled": true, "dev": true}, "asn1": {"version": "0.2.4", "bundled": true, "dev": true, "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "bundled": true, "dev": true}, "asynckit": {"version": "0.4.0", "bundled": true, "dev": true}, "aws-sign2": {"version": "0.7.0", "bundled": true, "dev": true}, "aws4": {"version": "1.8.0", "bundled": true, "dev": true}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true}, "bcrypt-pbkdf": {"version": "1.0.2", "bundled": true, "dev": true, "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "bin-links": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "write-file-atomic": "^2.3.0"}}, "bluebird": {"version": "3.5.5", "bundled": true, "dev": true}, "boxen": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "buffer-from": {"version": "1.0.0", "bundled": true, "dev": true}, "builtins": {"version": "1.0.3", "bundled": true, "dev": true}, "byline": {"version": "5.0.0", "bundled": true, "dev": true}, "byte-size": {"version": "5.0.1", "bundled": true, "dev": true}, "cacache": {"version": "12.0.2", "bundled": true, "dev": true, "requires": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "call-limit": {"version": "1.1.1", "bundled": true, "dev": true}, "camelcase": {"version": "4.1.0", "bundled": true, "dev": true}, "capture-stack-trace": {"version": "1.0.0", "bundled": true, "dev": true}, "caseless": {"version": "0.12.0", "bundled": true, "dev": true}, "chalk": {"version": "2.4.1", "bundled": true, "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chownr": {"version": "1.1.2", "bundled": true, "dev": true}, "ci-info": {"version": "2.0.0", "bundled": true, "dev": true}, "cidr-regex": {"version": "2.0.10", "bundled": true, "dev": true, "requires": {"ip-regex": "^2.1.0"}}, "cli-boxes": {"version": "1.0.0", "bundled": true, "dev": true}, "cli-columns": {"version": "3.1.2", "bundled": true, "dev": true, "requires": {"string-width": "^2.0.0", "strip-ansi": "^3.0.1"}}, "cli-table3": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"colors": "^1.1.2", "object-assign": "^4.1.0", "string-width": "^2.1.1"}}, "cliui": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "clone": {"version": "1.0.4", "bundled": true, "dev": true}, "cmd-shim": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "co": {"version": "4.6.0", "bundled": true, "dev": true}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "color-convert": {"version": "1.9.1", "bundled": true, "dev": true, "requires": {"color-name": "^1.1.1"}}, "color-name": {"version": "1.1.3", "bundled": true, "dev": true}, "colors": {"version": "1.3.3", "bundled": true, "dev": true, "optional": true}, "columnify": {"version": "1.5.4", "bundled": true, "dev": true, "requires": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}}, "combined-stream": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "concat-stream": {"version": "1.6.2", "bundled": true, "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "config-chain": {"version": "1.1.12", "bundled": true, "dev": true, "requires": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "configstore": {"version": "3.1.2", "bundled": true, "dev": true, "requires": {"dot-prop": "^4.1.0", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}}, "console-control-strings": {"version": "1.1.0", "bundled": true, "dev": true}, "copy-concurrently": {"version": "1.0.5", "bundled": true, "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}, "dependencies": {"aproba": {"version": "1.2.0", "bundled": true, "dev": true}, "iferr": {"version": "0.1.5", "bundled": true, "dev": true}}}, "core-util-is": {"version": "1.0.2", "bundled": true, "dev": true}, "create-error-class": {"version": "3.0.2", "bundled": true, "dev": true, "requires": {"capture-stack-trace": "^1.0.0"}}, "cross-spawn": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"lru-cache": {"version": "4.1.5", "bundled": true, "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "yallist": {"version": "2.1.2", "bundled": true, "dev": true}}}, "crypto-random-string": {"version": "1.0.0", "bundled": true, "dev": true}, "cyclist": {"version": "0.2.2", "bundled": true, "dev": true}, "dashdash": {"version": "1.14.1", "bundled": true, "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "debug": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "bundled": true, "dev": true}}}, "debuglog": {"version": "1.0.1", "bundled": true, "dev": true}, "decamelize": {"version": "1.2.0", "bundled": true, "dev": true}, "decode-uri-component": {"version": "0.2.0", "bundled": true, "dev": true}, "deep-extend": {"version": "0.5.1", "bundled": true, "dev": true}, "defaults": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"clone": "^1.0.2"}}, "define-properties": {"version": "1.1.3", "bundled": true, "dev": true, "requires": {"object-keys": "^1.0.12"}}, "delayed-stream": {"version": "1.0.0", "bundled": true, "dev": true}, "delegates": {"version": "1.0.0", "bundled": true, "dev": true}, "detect-indent": {"version": "5.0.0", "bundled": true, "dev": true}, "detect-newline": {"version": "2.1.0", "bundled": true, "dev": true}, "dezalgo": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"asap": "^2.0.0", "wrappy": "1"}}, "dot-prop": {"version": "4.2.0", "bundled": true, "dev": true, "requires": {"is-obj": "^1.0.0"}}, "dotenv": {"version": "5.0.1", "bundled": true, "dev": true}, "duplexer3": {"version": "0.1.4", "bundled": true, "dev": true}, "duplexify": {"version": "3.6.0", "bundled": true, "dev": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "ecc-jsbn": {"version": "0.1.2", "bundled": true, "dev": true, "optional": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "editor": {"version": "1.0.0", "bundled": true, "dev": true}, "encoding": {"version": "0.1.12", "bundled": true, "dev": true, "requires": {"iconv-lite": "~0.4.13"}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "dev": true, "requires": {"once": "^1.4.0"}}, "env-paths": {"version": "1.0.0", "bundled": true, "dev": true}, "err-code": {"version": "1.1.2", "bundled": true, "dev": true}, "errno": {"version": "0.1.7", "bundled": true, "dev": true, "requires": {"prr": "~1.0.1"}}, "es-abstract": {"version": "1.12.0", "bundled": true, "dev": true, "requires": {"es-to-primitive": "^1.1.1", "function-bind": "^1.1.1", "has": "^1.0.1", "is-callable": "^1.1.3", "is-regex": "^1.0.4"}}, "es-to-primitive": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "es6-promise": {"version": "4.2.8", "bundled": true, "dev": true}, "es6-promisify": {"version": "5.0.0", "bundled": true, "dev": true, "requires": {"es6-promise": "^4.0.3"}}, "escape-string-regexp": {"version": "1.0.5", "bundled": true, "dev": true}, "execa": {"version": "0.7.0", "bundled": true, "dev": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"get-stream": {"version": "3.0.0", "bundled": true, "dev": true}}}, "extend": {"version": "3.0.2", "bundled": true, "dev": true}, "extsprintf": {"version": "1.3.0", "bundled": true, "dev": true}, "fast-deep-equal": {"version": "1.1.0", "bundled": true, "dev": true}, "fast-json-stable-stringify": {"version": "2.0.0", "bundled": true, "dev": true}, "figgy-pudding": {"version": "3.5.1", "bundled": true, "dev": true}, "find-npm-prefix": {"version": "1.0.2", "bundled": true, "dev": true}, "find-up": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"locate-path": "^2.0.0"}}, "flush-write-stream": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "forever-agent": {"version": "0.6.1", "bundled": true, "dev": true}, "form-data": {"version": "2.3.2", "bundled": true, "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "1.0.6", "mime-types": "^2.1.12"}}, "from2": {"version": "2.3.0", "bundled": true, "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "fs-minipass": {"version": "1.2.6", "bundled": true, "dev": true, "requires": {"minipass": "^2.2.1"}}, "fs-vacuum": {"version": "1.2.10", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "fs-write-stream-atomic": {"version": "1.0.10", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}, "dependencies": {"iferr": {"version": "0.1.5", "bundled": true, "dev": true}, "readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "function-bind": {"version": "1.1.1", "bundled": true, "dev": true}, "gauge": {"version": "2.7.4", "bundled": true, "dev": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}, "dependencies": {"aproba": {"version": "1.2.0", "bundled": true, "dev": true}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}}}, "genfun": {"version": "5.0.0", "bundled": true, "dev": true}, "gentle-fs": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"aproba": "^1.1.2", "fs-vacuum": "^1.2.10", "graceful-fs": "^4.1.11", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "path-is-inside": "^1.0.2", "read-cmd-shim": "^1.0.1", "slide": "^1.1.6"}, "dependencies": {"aproba": {"version": "1.2.0", "bundled": true, "dev": true}, "iferr": {"version": "0.1.5", "bundled": true, "dev": true}}}, "get-caller-file": {"version": "1.0.2", "bundled": true, "dev": true}, "get-stream": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"pump": "^3.0.0"}}, "getpass": {"version": "0.1.7", "bundled": true, "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.1.4", "bundled": true, "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "global-dirs": {"version": "0.1.1", "bundled": true, "dev": true, "requires": {"ini": "^1.3.4"}}, "got": {"version": "6.7.1", "bundled": true, "dev": true, "requires": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "dependencies": {"get-stream": {"version": "3.0.0", "bundled": true, "dev": true}}}, "graceful-fs": {"version": "4.2.0", "bundled": true, "dev": true}, "har-schema": {"version": "2.0.0", "bundled": true, "dev": true}, "har-validator": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"ajv": "^5.3.0", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "bundled": true, "dev": true}, "has-symbols": {"version": "1.0.0", "bundled": true, "dev": true}, "has-unicode": {"version": "2.0.1", "bundled": true, "dev": true}, "hosted-git-info": {"version": "2.8.2", "bundled": true, "dev": true, "requires": {"lru-cache": "^5.1.1"}}, "http-cache-semantics": {"version": "3.8.1", "bundled": true, "dev": true}, "http-proxy-agent": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"agent-base": "4", "debug": "3.1.0"}}, "http-signature": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-proxy-agent": {"version": "2.2.2", "bundled": true, "dev": true, "requires": {"agent-base": "^4.3.0", "debug": "^3.1.0"}}, "humanize-ms": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"ms": "^2.0.0"}}, "iconv-lite": {"version": "0.4.23", "bundled": true, "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "iferr": {"version": "1.0.2", "bundled": true, "dev": true}, "ignore-walk": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"minimatch": "^3.0.4"}}, "import-lazy": {"version": "2.1.0", "bundled": true, "dev": true}, "imurmurhash": {"version": "0.1.4", "bundled": true, "dev": true}, "infer-owner": {"version": "1.0.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "bundled": true, "dev": true}, "ini": {"version": "1.3.5", "bundled": true, "dev": true}, "init-package-json": {"version": "1.10.3", "bundled": true, "dev": true, "requires": {"glob": "^7.1.1", "npm-package-arg": "^4.0.0 || ^5.0.0 || ^6.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^3.0.0"}}, "invert-kv": {"version": "1.0.0", "bundled": true, "dev": true}, "ip": {"version": "1.1.5", "bundled": true, "dev": true}, "ip-regex": {"version": "2.1.0", "bundled": true, "dev": true}, "is-callable": {"version": "1.1.4", "bundled": true, "dev": true}, "is-ci": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"ci-info": "^1.0.0"}, "dependencies": {"ci-info": {"version": "1.6.0", "bundled": true, "dev": true}}}, "is-cidr": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"cidr-regex": "^2.0.10"}}, "is-date-object": {"version": "1.0.1", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "is-installed-globally": {"version": "0.1.0", "bundled": true, "dev": true, "requires": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}}, "is-npm": {"version": "1.0.0", "bundled": true, "dev": true}, "is-obj": {"version": "1.0.1", "bundled": true, "dev": true}, "is-path-inside": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-redirect": {"version": "1.0.0", "bundled": true, "dev": true}, "is-regex": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"has": "^1.0.1"}}, "is-retry-allowed": {"version": "1.1.0", "bundled": true, "dev": true}, "is-stream": {"version": "1.1.0", "bundled": true, "dev": true}, "is-symbol": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"has-symbols": "^1.0.0"}}, "is-typedarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isarray": {"version": "1.0.0", "bundled": true, "dev": true}, "isexe": {"version": "2.0.0", "bundled": true, "dev": true}, "isstream": {"version": "0.1.2", "bundled": true, "dev": true}, "jsbn": {"version": "0.1.1", "bundled": true, "dev": true, "optional": true}, "json-parse-better-errors": {"version": "1.0.2", "bundled": true, "dev": true}, "json-schema": {"version": "0.2.3", "bundled": true, "dev": true}, "json-schema-traverse": {"version": "0.3.1", "bundled": true, "dev": true}, "json-stringify-safe": {"version": "5.0.1", "bundled": true, "dev": true}, "jsonparse": {"version": "1.3.1", "bundled": true, "dev": true}, "jsprim": {"version": "1.4.1", "bundled": true, "dev": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "latest-version": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"package-json": "^4.0.0"}}, "lazy-property": {"version": "1.0.0", "bundled": true, "dev": true}, "lcid": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "libcipm": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"bin-links": "^1.1.2", "bluebird": "^3.5.1", "figgy-pudding": "^3.5.1", "find-npm-prefix": "^1.0.2", "graceful-fs": "^4.1.11", "ini": "^1.3.5", "lock-verify": "^2.0.2", "mkdirp": "^0.5.1", "npm-lifecycle": "^3.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.1.0", "pacote": "^9.1.0", "read-package-json": "^2.0.13", "rimraf": "^2.6.2", "worker-farm": "^1.6.0"}}, "libnpm": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"bin-links": "^1.1.2", "bluebird": "^3.5.3", "find-npm-prefix": "^1.0.2", "libnpmaccess": "^3.0.2", "libnpmconfig": "^1.2.1", "libnpmhook": "^5.0.3", "libnpmorg": "^1.0.1", "libnpmpublish": "^1.1.2", "libnpmsearch": "^2.0.2", "libnpmteam": "^1.0.2", "lock-verify": "^2.0.2", "npm-lifecycle": "^3.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.1.0", "npm-profile": "^4.0.2", "npm-registry-fetch": "^4.0.0", "npmlog": "^4.1.2", "pacote": "^9.5.3", "read-package-json": "^2.0.13", "stringify-package": "^1.0.0"}}, "libnpmaccess": {"version": "3.0.2", "bundled": true, "dev": true, "requires": {"aproba": "^2.0.0", "get-stream": "^4.0.0", "npm-package-arg": "^6.1.0", "npm-registry-fetch": "^4.0.0"}}, "libnpmconfig": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"figgy-pudding": "^3.5.1", "find-up": "^3.0.0", "ini": "^1.3.5"}, "dependencies": {"find-up": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.2.0", "bundled": true, "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "bundled": true, "dev": true}}}, "libnpmhook": {"version": "5.0.3", "bundled": true, "dev": true, "requires": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "libnpmorg": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "libnpmpublish": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"aproba": "^2.0.0", "figgy-pudding": "^3.5.1", "get-stream": "^4.0.0", "lodash.clonedeep": "^4.5.0", "normalize-package-data": "^2.4.0", "npm-package-arg": "^6.1.0", "npm-registry-fetch": "^4.0.0", "semver": "^5.5.1", "ssri": "^6.0.1"}}, "libnpmsearch": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"figgy-pudding": "^3.5.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "libnpmteam": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "libnpx": {"version": "10.2.0", "bundled": true, "dev": true, "requires": {"dotenv": "^5.0.1", "npm-package-arg": "^6.0.0", "rimraf": "^2.6.2", "safe-buffer": "^5.1.0", "update-notifier": "^2.3.0", "which": "^1.3.0", "y18n": "^4.0.0", "yargs": "^11.0.0"}}, "locate-path": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "lock-verify": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"npm-package-arg": "^6.1.0", "semver": "^5.4.1"}}, "lockfile": {"version": "1.0.4", "bundled": true, "dev": true, "requires": {"signal-exit": "^3.0.2"}}, "lodash._baseindexof": {"version": "3.1.0", "bundled": true, "dev": true}, "lodash._baseuniq": {"version": "4.6.0", "bundled": true, "dev": true, "requires": {"lodash._createset": "~4.0.0", "lodash._root": "~3.0.0"}}, "lodash._bindcallback": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash._cacheindexof": {"version": "3.0.2", "bundled": true, "dev": true}, "lodash._createcache": {"version": "3.1.2", "bundled": true, "dev": true, "requires": {"lodash._getnative": "^3.0.0"}}, "lodash._createset": {"version": "4.0.3", "bundled": true, "dev": true}, "lodash._getnative": {"version": "3.9.1", "bundled": true, "dev": true}, "lodash._root": {"version": "3.0.1", "bundled": true, "dev": true}, "lodash.clonedeep": {"version": "4.5.0", "bundled": true, "dev": true}, "lodash.restparam": {"version": "3.6.1", "bundled": true, "dev": true}, "lodash.union": {"version": "4.6.0", "bundled": true, "dev": true}, "lodash.uniq": {"version": "4.5.0", "bundled": true, "dev": true}, "lodash.without": {"version": "4.4.0", "bundled": true, "dev": true}, "lowercase-keys": {"version": "1.0.1", "bundled": true, "dev": true}, "lru-cache": {"version": "5.1.1", "bundled": true, "dev": true, "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"pify": "^3.0.0"}}, "make-fetch-happen": {"version": "5.0.0", "bundled": true, "dev": true, "requires": {"agentkeepalive": "^3.4.1", "cacache": "^12.0.0", "http-cache-semantics": "^3.8.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^4.0.0", "ssri": "^6.0.0"}}, "meant": {"version": "1.0.1", "bundled": true, "dev": true}, "mem": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"mimic-fn": "^1.0.0"}}, "mime-db": {"version": "1.35.0", "bundled": true, "dev": true}, "mime-types": {"version": "2.1.19", "bundled": true, "dev": true, "requires": {"mime-db": "~1.35.0"}}, "mimic-fn": {"version": "1.2.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.8", "bundled": true, "dev": true}, "minipass": {"version": "2.3.3", "bundled": true, "dev": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "dependencies": {"yallist": {"version": "3.0.2", "bundled": true, "dev": true}}}, "minizlib": {"version": "1.2.1", "bundled": true, "dev": true, "requires": {"minipass": "^2.2.1"}}, "mississippi": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}}, "move-concurrently": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}, "dependencies": {"aproba": {"version": "1.2.0", "bundled": true, "dev": true}}}, "ms": {"version": "2.1.1", "bundled": true, "dev": true}, "mute-stream": {"version": "0.0.7", "bundled": true, "dev": true}, "node-fetch-npm": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}}, "node-gyp": {"version": "5.0.3", "bundled": true, "dev": true, "requires": {"env-paths": "^1.0.0", "glob": "^7.0.3", "graceful-fs": "^4.1.2", "mkdirp": "^0.5.0", "nopt": "2 || 3", "npmlog": "0 || 1 || 2 || 3 || 4", "request": "^2.87.0", "rimraf": "2", "semver": "~5.3.0", "tar": "^4.4.8", "which": "1"}, "dependencies": {"nopt": {"version": "3.0.6", "bundled": true, "dev": true, "requires": {"abbrev": "1"}}, "semver": {"version": "5.3.0", "bundled": true, "dev": true}}}, "nopt": {"version": "4.0.1", "bundled": true, "dev": true, "requires": {"abbrev": "1", "osenv": "^0.1.4"}}, "normalize-package-data": {"version": "2.5.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"resolve": {"version": "1.10.0", "bundled": true, "dev": true, "requires": {"path-parse": "^1.0.6"}}}}, "npm-audit-report": {"version": "1.3.2", "bundled": true, "dev": true, "requires": {"cli-table3": "^0.5.0", "console-control-strings": "^1.1.0"}}, "npm-bundled": {"version": "1.0.6", "bundled": true, "dev": true}, "npm-cache-filename": {"version": "1.0.2", "bundled": true, "dev": true}, "npm-install-checks": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"semver": "^2.3.0 || 3.x || 4 || 5"}}, "npm-lifecycle": {"version": "3.1.2", "bundled": true, "dev": true, "requires": {"byline": "^5.0.0", "graceful-fs": "^4.1.15", "node-gyp": "^5.0.2", "resolve-from": "^4.0.0", "slide": "^1.1.6", "uid-number": "0.0.6", "umask": "^1.1.0", "which": "^1.3.1"}}, "npm-logical-tree": {"version": "1.2.1", "bundled": true, "dev": true}, "npm-package-arg": {"version": "6.1.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "^2.6.0", "osenv": "^0.1.5", "semver": "^5.5.0", "validate-npm-package-name": "^3.0.0"}}, "npm-packlist": {"version": "1.4.4", "bundled": true, "dev": true, "requires": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "npm-pick-manifest": {"version": "2.2.3", "bundled": true, "dev": true, "requires": {"figgy-pudding": "^3.5.1", "npm-package-arg": "^6.0.0", "semver": "^5.4.1"}}, "npm-profile": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"aproba": "^1.1.2 || 2", "figgy-pudding": "^3.4.1", "npm-registry-fetch": "^4.0.0"}}, "npm-registry-fetch": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"JSONStream": "^1.3.4", "bluebird": "^3.5.1", "figgy-pudding": "^3.4.1", "lru-cache": "^5.1.1", "make-fetch-happen": "^5.0.0", "npm-package-arg": "^6.1.0"}}, "npm-run-path": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"path-key": "^2.0.0"}}, "npm-user-validate": {"version": "1.0.0", "bundled": true, "dev": true}, "npmlog": {"version": "4.1.2", "bundled": true, "dev": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "oauth-sign": {"version": "0.9.0", "bundled": true, "dev": true}, "object-assign": {"version": "4.1.1", "bundled": true, "dev": true}, "object-keys": {"version": "1.0.12", "bundled": true, "dev": true}, "object.getownpropertydescriptors": {"version": "2.0.3", "bundled": true, "dev": true, "requires": {"define-properties": "^1.1.2", "es-abstract": "^1.5.1"}}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1"}}, "opener": {"version": "1.5.1", "bundled": true, "dev": true}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true}, "os-locale": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"execa": "^0.7.0", "lcid": "^1.0.0", "mem": "^1.1.0"}}, "os-tmpdir": {"version": "1.0.2", "bundled": true, "dev": true}, "osenv": {"version": "0.1.5", "bundled": true, "dev": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "p-finally": {"version": "1.0.0", "bundled": true, "dev": true}, "p-limit": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "bundled": true, "dev": true}, "package-json": {"version": "4.0.1", "bundled": true, "dev": true, "requires": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}}, "pacote": {"version": "9.5.4", "bundled": true, "dev": true, "requires": {"bluebird": "^3.5.3", "cacache": "^12.0.0", "figgy-pudding": "^3.5.1", "get-stream": "^4.1.0", "glob": "^7.1.3", "lru-cache": "^5.1.1", "make-fetch-happen": "^5.0.0", "minimatch": "^3.0.4", "minipass": "^2.3.5", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "normalize-package-data": "^2.4.0", "npm-package-arg": "^6.1.0", "npm-packlist": "^1.1.12", "npm-pick-manifest": "^2.2.3", "npm-registry-fetch": "^4.0.0", "osenv": "^0.1.5", "promise-inflight": "^1.0.1", "promise-retry": "^1.1.1", "protoduck": "^5.0.1", "rimraf": "^2.6.2", "safe-buffer": "^5.1.2", "semver": "^5.6.0", "ssri": "^6.0.1", "tar": "^4.4.8", "unique-filename": "^1.1.1", "which": "^1.3.1"}, "dependencies": {"minipass": {"version": "2.3.5", "bundled": true, "dev": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}}}, "parallel-transform": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "path-exists": {"version": "3.0.0", "bundled": true, "dev": true}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "path-is-inside": {"version": "1.0.2", "bundled": true, "dev": true}, "path-key": {"version": "2.0.1", "bundled": true, "dev": true}, "path-parse": {"version": "1.0.6", "bundled": true, "dev": true}, "performance-now": {"version": "2.1.0", "bundled": true, "dev": true}, "pify": {"version": "3.0.0", "bundled": true, "dev": true}, "prepend-http": {"version": "1.0.4", "bundled": true, "dev": true}, "process-nextick-args": {"version": "2.0.0", "bundled": true, "dev": true}, "promise-inflight": {"version": "1.0.1", "bundled": true, "dev": true}, "promise-retry": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "dependencies": {"retry": {"version": "0.10.1", "bundled": true, "dev": true}}}, "promzard": {"version": "0.3.0", "bundled": true, "dev": true, "requires": {"read": "1"}}, "proto-list": {"version": "1.2.4", "bundled": true, "dev": true}, "protoduck": {"version": "5.0.1", "bundled": true, "dev": true, "requires": {"genfun": "^5.0.0"}}, "prr": {"version": "1.0.1", "bundled": true, "dev": true}, "pseudomap": {"version": "1.0.2", "bundled": true, "dev": true}, "psl": {"version": "1.1.29", "bundled": true, "dev": true}, "pump": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "bundled": true, "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "bundled": true, "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "punycode": {"version": "1.4.1", "bundled": true, "dev": true}, "qrcode-terminal": {"version": "0.12.0", "bundled": true, "dev": true}, "qs": {"version": "6.5.2", "bundled": true, "dev": true}, "query-string": {"version": "6.8.2", "bundled": true, "dev": true, "requires": {"decode-uri-component": "^0.2.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}}, "qw": {"version": "1.0.1", "bundled": true, "dev": true}, "rc": {"version": "1.2.7", "bundled": true, "dev": true, "requires": {"deep-extend": "^0.5.1", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "bundled": true, "dev": true}}}, "read": {"version": "1.0.7", "bundled": true, "dev": true, "requires": {"mute-stream": "~0.0.4"}}, "read-cmd-shim": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2"}}, "read-installed": {"version": "4.0.3", "bundled": true, "dev": true, "requires": {"debuglog": "^1.0.1", "graceful-fs": "^4.1.2", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}}, "read-package-json": {"version": "2.0.13", "bundled": true, "dev": true, "requires": {"glob": "^7.1.1", "graceful-fs": "^4.1.2", "json-parse-better-errors": "^1.0.1", "normalize-package-data": "^2.0.0", "slash": "^1.0.0"}}, "read-package-tree": {"version": "5.3.1", "bundled": true, "dev": true, "requires": {"read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "util-promisify": "^2.1.0"}}, "readable-stream": {"version": "3.4.0", "bundled": true, "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readdir-scoped-modules": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "registry-auth-token": {"version": "3.3.2", "bundled": true, "dev": true, "requires": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "registry-url": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"rc": "^1.0.1"}}, "request": {"version": "2.88.0", "bundled": true, "dev": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "require-directory": {"version": "2.1.1", "bundled": true, "dev": true}, "require-main-filename": {"version": "1.0.1", "bundled": true, "dev": true}, "resolve-from": {"version": "4.0.0", "bundled": true, "dev": true}, "retry": {"version": "0.12.0", "bundled": true, "dev": true}, "rimraf": {"version": "2.6.3", "bundled": true, "dev": true, "requires": {"glob": "^7.1.3"}}, "run-queue": {"version": "1.0.3", "bundled": true, "dev": true, "requires": {"aproba": "^1.1.1"}, "dependencies": {"aproba": {"version": "1.2.0", "bundled": true, "dev": true}}}, "safe-buffer": {"version": "5.1.2", "bundled": true, "dev": true}, "safer-buffer": {"version": "2.1.2", "bundled": true, "dev": true}, "semver": {"version": "5.7.0", "bundled": true, "dev": true}, "semver-diff": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"semver": "^5.0.3"}}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true}, "sha": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2"}}, "shebang-command": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "bundled": true, "dev": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true}, "slash": {"version": "1.0.0", "bundled": true, "dev": true}, "slide": {"version": "1.1.6", "bundled": true, "dev": true}, "smart-buffer": {"version": "4.0.2", "bundled": true, "dev": true}, "socks": {"version": "2.3.2", "bundled": true, "dev": true, "requires": {"ip": "^1.1.5", "smart-buffer": "4.0.2"}}, "socks-proxy-agent": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"agent-base": "~4.2.1", "socks": "~2.3.2"}, "dependencies": {"agent-base": {"version": "4.2.1", "bundled": true, "dev": true, "requires": {"es6-promisify": "^5.0.0"}}}}, "sorted-object": {"version": "2.0.1", "bundled": true, "dev": true}, "sorted-union-stream": {"version": "2.1.3", "bundled": true, "dev": true, "requires": {"from2": "^1.3.0", "stream-iterate": "^1.1.0"}, "dependencies": {"from2": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "~1.1.10"}}, "isarray": {"version": "0.0.1", "bundled": true, "dev": true}, "readable-stream": {"version": "1.1.14", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "bundled": true, "dev": true}}}, "spdx-correct": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.1.0", "bundled": true, "dev": true}, "spdx-expression-parse": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.3", "bundled": true, "dev": true}, "split-on-first": {"version": "1.1.0", "bundled": true, "dev": true}, "sshpk": {"version": "1.14.2", "bundled": true, "dev": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "ssri": {"version": "6.0.1", "bundled": true, "dev": true, "requires": {"figgy-pudding": "^3.5.1"}}, "stream-each": {"version": "1.2.2", "bundled": true, "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-iterate": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"readable-stream": "^2.1.5", "stream-shift": "^1.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "stream-shift": {"version": "1.0.0", "bundled": true, "dev": true}, "strict-uri-encode": {"version": "2.0.0", "bundled": true, "dev": true}, "string-width": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "bundled": true, "dev": true}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "string_decoder": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}, "stringify-package": {"version": "1.0.0", "bundled": true, "dev": true}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-eof": {"version": "1.0.0", "bundled": true, "dev": true}, "strip-json-comments": {"version": "2.0.1", "bundled": true, "dev": true}, "supports-color": {"version": "5.4.0", "bundled": true, "dev": true, "requires": {"has-flag": "^3.0.0"}}, "tar": {"version": "4.4.10", "bundled": true, "dev": true, "requires": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.5", "minizlib": "^1.2.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.3"}, "dependencies": {"minipass": {"version": "2.3.5", "bundled": true, "dev": true, "requires": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "yallist": {"version": "3.0.3", "bundled": true, "dev": true}}}, "term-size": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"execa": "^0.7.0"}}, "text-table": {"version": "0.2.0", "bundled": true, "dev": true}, "through": {"version": "2.3.8", "bundled": true, "dev": true}, "through2": {"version": "2.0.3", "bundled": true, "dev": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}, "dependencies": {"readable-stream": {"version": "2.3.6", "bundled": true, "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "timed-out": {"version": "4.0.1", "bundled": true, "dev": true}, "tiny-relative-date": {"version": "1.3.0", "bundled": true, "dev": true}, "tough-cookie": {"version": "2.4.3", "bundled": true, "dev": true, "requires": {"psl": "^1.1.24", "punycode": "^1.4.1"}}, "tunnel-agent": {"version": "0.6.0", "bundled": true, "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "bundled": true, "dev": true, "optional": true}, "typedarray": {"version": "0.0.6", "bundled": true, "dev": true}, "uid-number": {"version": "0.0.6", "bundled": true, "dev": true}, "umask": {"version": "1.1.0", "bundled": true, "dev": true}, "unique-filename": {"version": "1.1.1", "bundled": true, "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "unique-string": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"crypto-random-string": "^1.0.0"}}, "unpipe": {"version": "1.0.0", "bundled": true, "dev": true}, "unzip-response": {"version": "2.0.1", "bundled": true, "dev": true}, "update-notifier": {"version": "2.5.0", "bundled": true, "dev": true, "requires": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-ci": "^1.0.10", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}}, "url-parse-lax": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"prepend-http": "^1.0.1"}}, "util-deprecate": {"version": "1.0.2", "bundled": true, "dev": true}, "util-extend": {"version": "1.0.3", "bundled": true, "dev": true}, "util-promisify": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"object.getownpropertydescriptors": "^2.0.3"}}, "uuid": {"version": "3.3.2", "bundled": true, "dev": true}, "validate-npm-package-license": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "validate-npm-package-name": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"builtins": "^1.0.3"}}, "verror": {"version": "1.10.0", "bundled": true, "dev": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "wcwidth": {"version": "1.0.1", "bundled": true, "dev": true, "requires": {"defaults": "^1.0.3"}}, "which": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "bundled": true, "dev": true}, "wide-align": {"version": "1.1.2", "bundled": true, "dev": true, "requires": {"string-width": "^1.0.2"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}}}, "widest-line": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.1.1"}}, "worker-farm": {"version": "1.7.0", "bundled": true, "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}, "write-file-atomic": {"version": "2.4.3", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "xdg-basedir": {"version": "3.0.0", "bundled": true, "dev": true}, "xtend": {"version": "4.0.1", "bundled": true, "dev": true}, "y18n": {"version": "4.0.0", "bundled": true, "dev": true}, "yallist": {"version": "3.0.3", "bundled": true, "dev": true}, "yargs": {"version": "11.0.0", "bundled": true, "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^1.1.1", "find-up": "^2.1.0", "get-caller-file": "^1.0.1", "os-locale": "^2.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1", "yargs-parser": "^9.0.2"}, "dependencies": {"y18n": {"version": "3.2.1", "bundled": true, "dev": true}}}, "yargs-parser": {"version": "9.0.2", "bundled": true, "dev": true, "requires": {"camelcase": "^4.1.0"}}}}, "npm-run-path": {"version": "2.0.2", "resolved": "http://npm.mail.netease.com/registry/npm-run-path/download/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/number-is-nan/download/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="}, "nyc": {"version": "13.3.0", "resolved": "http://npm.mail.netease.com/registry/nyc/download/nyc-13.3.0.tgz", "integrity": "sha1-2k2+kanIuerT9PM0THbzU+PHjHU=", "dev": true, "requires": {"archy": "^1.0.0", "arrify": "^1.0.1", "caching-transform": "^3.0.1", "convert-source-map": "^1.6.0", "find-cache-dir": "^2.0.0", "find-up": "^3.0.0", "foreground-child": "^1.5.6", "glob": "^7.1.3", "istanbul-lib-coverage": "^2.0.3", "istanbul-lib-hook": "^2.0.3", "istanbul-lib-instrument": "^3.1.0", "istanbul-lib-report": "^2.0.4", "istanbul-lib-source-maps": "^3.0.2", "istanbul-reports": "^2.1.1", "make-dir": "^1.3.0", "merge-source-map": "^1.1.0", "resolve-from": "^4.0.0", "rimraf": "^2.6.3", "signal-exit": "^3.0.2", "spawn-wrap": "^1.4.2", "test-exclude": "^5.1.0", "uuid": "^3.3.2", "yargs": "^12.0.5", "yargs-parser": "^11.1.1"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "bundled": true, "dev": true}, "append-transform": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"default-require-extensions": "^2.0.0"}}, "archy": {"version": "1.0.0", "bundled": true, "dev": true}, "arrify": {"version": "1.0.1", "bundled": true, "dev": true}, "async": {"version": "2.6.2", "bundled": true, "dev": true, "requires": {"lodash": "^4.17.11"}}, "balanced-match": {"version": "1.0.0", "bundled": true, "dev": true}, "brace-expansion": {"version": "1.1.11", "bundled": true, "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "caching-transform": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"hasha": "^3.0.0", "make-dir": "^1.3.0", "package-hash": "^3.0.0", "write-file-atomic": "^2.3.0"}}, "camelcase": {"version": "5.0.0", "bundled": true, "dev": true}, "cliui": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "code-point-at": {"version": "1.1.0", "bundled": true, "dev": true}, "commander": {"version": "2.17.1", "bundled": true, "dev": true, "optional": true}, "commondir": {"version": "1.0.1", "bundled": true, "dev": true}, "concat-map": {"version": "0.0.1", "bundled": true, "dev": true}, "convert-source-map": {"version": "1.6.0", "bundled": true, "dev": true, "requires": {"safe-buffer": "~5.1.1"}}, "cross-spawn": {"version": "4.0.2", "bundled": true, "dev": true, "requires": {"lru-cache": "^4.0.1", "which": "^1.2.9"}}, "debug": {"version": "4.1.1", "bundled": true, "dev": true, "requires": {"ms": "^2.1.1"}}, "decamelize": {"version": "1.2.0", "bundled": true, "dev": true}, "default-require-extensions": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"strip-bom": "^3.0.0"}}, "end-of-stream": {"version": "1.4.1", "bundled": true, "dev": true, "requires": {"once": "^1.4.0"}}, "error-ex": {"version": "1.3.2", "bundled": true, "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "es6-error": {"version": "4.1.1", "bundled": true, "dev": true}, "execa": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "bundled": true, "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}}}, "find-cache-dir": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^1.0.0", "pkg-dir": "^3.0.0"}}, "find-up": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"locate-path": "^3.0.0"}}, "foreground-child": {"version": "1.5.6", "bundled": true, "dev": true, "requires": {"cross-spawn": "^4", "signal-exit": "^3.0.0"}}, "fs.realpath": {"version": "1.0.0", "bundled": true, "dev": true}, "get-caller-file": {"version": "1.0.3", "bundled": true, "dev": true}, "get-stream": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"pump": "^3.0.0"}}, "glob": {"version": "7.1.3", "bundled": true, "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "graceful-fs": {"version": "4.1.15", "bundled": true, "dev": true}, "handlebars": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"async": "^2.5.0", "optimist": "^0.6.1", "source-map": "^0.6.1", "uglify-js": "^3.1.4"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true}}}, "has-flag": {"version": "3.0.0", "bundled": true, "dev": true}, "hasha": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"is-stream": "^1.0.1"}}, "hosted-git-info": {"version": "2.7.1", "bundled": true, "dev": true}, "imurmurhash": {"version": "0.1.4", "bundled": true, "dev": true}, "inflight": {"version": "1.0.6", "bundled": true, "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "bundled": true, "dev": true}, "invert-kv": {"version": "2.0.0", "bundled": true, "dev": true}, "is-arrayish": {"version": "0.2.1", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "bundled": true, "dev": true}, "is-stream": {"version": "1.1.0", "bundled": true, "dev": true}, "isexe": {"version": "2.0.0", "bundled": true, "dev": true}, "istanbul-lib-coverage": {"version": "2.0.3", "bundled": true, "dev": true}, "istanbul-lib-hook": {"version": "2.0.3", "bundled": true, "dev": true, "requires": {"append-transform": "^1.0.0"}}, "istanbul-lib-report": {"version": "2.0.4", "bundled": true, "dev": true, "requires": {"istanbul-lib-coverage": "^2.0.3", "make-dir": "^1.3.0", "supports-color": "^6.0.0"}, "dependencies": {"supports-color": {"version": "6.1.0", "bundled": true, "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "istanbul-lib-source-maps": {"version": "3.0.2", "bundled": true, "dev": true, "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^2.0.3", "make-dir": "^1.3.0", "rimraf": "^2.6.2", "source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true}}}, "istanbul-reports": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"handlebars": "^4.1.0"}}, "json-parse-better-errors": {"version": "1.0.2", "bundled": true, "dev": true}, "lcid": {"version": "2.0.0", "bundled": true, "dev": true, "requires": {"invert-kv": "^2.0.0"}}, "load-json-file": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.11", "bundled": true, "dev": true}, "lodash.flattendeep": {"version": "4.4.0", "bundled": true, "dev": true}, "lru-cache": {"version": "4.1.5", "bundled": true, "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-dir": {"version": "1.3.0", "bundled": true, "dev": true, "requires": {"pify": "^3.0.0"}}, "map-age-cleaner": {"version": "0.1.3", "bundled": true, "dev": true, "requires": {"p-defer": "^1.0.0"}}, "mem": {"version": "4.1.0", "bundled": true, "dev": true, "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^1.0.0", "p-is-promise": "^2.0.0"}}, "merge-source-map": {"version": "1.1.0", "bundled": true, "dev": true, "requires": {"source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true}}}, "mimic-fn": {"version": "1.2.0", "bundled": true, "dev": true}, "minimatch": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "0.0.10", "bundled": true, "dev": true}, "mkdirp": {"version": "0.5.1", "bundled": true, "dev": true, "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "bundled": true, "dev": true}}}, "ms": {"version": "2.1.1", "bundled": true, "dev": true}, "nice-try": {"version": "1.0.5", "bundled": true, "dev": true}, "normalize-package-data": {"version": "2.5.0", "bundled": true, "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "npm-run-path": {"version": "2.0.2", "bundled": true, "dev": true, "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "bundled": true, "dev": true}, "once": {"version": "1.4.0", "bundled": true, "dev": true, "requires": {"wrappy": "1"}}, "optimist": {"version": "0.6.1", "bundled": true, "dev": true, "requires": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "os-homedir": {"version": "1.0.2", "bundled": true, "dev": true}, "os-locale": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "p-defer": {"version": "1.0.0", "bundled": true, "dev": true}, "p-finally": {"version": "1.0.0", "bundled": true, "dev": true}, "p-is-promise": {"version": "2.0.0", "bundled": true, "dev": true}, "p-limit": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.0.0", "bundled": true, "dev": true}, "package-hash": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.15", "hasha": "^3.0.0", "lodash.flattendeep": "^4.4.0", "release-zalgo": "^1.0.0"}}, "parse-json": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "path-exists": {"version": "3.0.0", "bundled": true, "dev": true}, "path-is-absolute": {"version": "1.0.1", "bundled": true, "dev": true}, "path-key": {"version": "2.0.1", "bundled": true, "dev": true}, "path-parse": {"version": "1.0.6", "bundled": true, "dev": true}, "path-type": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"pify": "^3.0.0"}}, "pify": {"version": "3.0.0", "bundled": true, "dev": true}, "pkg-dir": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"find-up": "^3.0.0"}}, "pseudomap": {"version": "1.0.2", "bundled": true, "dev": true}, "pump": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "read-pkg": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"find-up": "^3.0.0", "read-pkg": "^3.0.0"}}, "release-zalgo": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"es6-error": "^4.0.1"}}, "require-directory": {"version": "2.1.1", "bundled": true, "dev": true}, "require-main-filename": {"version": "1.0.1", "bundled": true, "dev": true}, "resolve": {"version": "1.10.0", "bundled": true, "dev": true, "requires": {"path-parse": "^1.0.6"}}, "resolve-from": {"version": "4.0.0", "bundled": true, "dev": true}, "rimraf": {"version": "2.6.3", "bundled": true, "dev": true, "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.1.2", "bundled": true, "dev": true}, "semver": {"version": "5.6.0", "bundled": true, "dev": true}, "set-blocking": {"version": "2.0.0", "bundled": true, "dev": true}, "shebang-command": {"version": "1.2.0", "bundled": true, "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "bundled": true, "dev": true}, "signal-exit": {"version": "3.0.2", "bundled": true, "dev": true}, "spawn-wrap": {"version": "1.4.2", "bundled": true, "dev": true, "requires": {"foreground-child": "^1.5.6", "mkdirp": "^0.5.0", "os-homedir": "^1.0.1", "rimraf": "^2.6.2", "signal-exit": "^3.0.2", "which": "^1.3.0"}}, "spdx-correct": {"version": "3.1.0", "bundled": true, "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.2.0", "bundled": true, "dev": true}, "spdx-expression-parse": {"version": "3.0.0", "bundled": true, "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.3", "bundled": true, "dev": true}, "string-width": {"version": "2.1.1", "bundled": true, "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "bundled": true, "dev": true, "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "3.0.0", "bundled": true, "dev": true}, "strip-eof": {"version": "1.0.0", "bundled": true, "dev": true}, "test-exclude": {"version": "5.1.0", "bundled": true, "dev": true, "requires": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^1.0.1"}}, "uglify-js": {"version": "3.4.9", "bundled": true, "dev": true, "optional": true, "requires": {"commander": "~2.17.1", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "bundled": true, "dev": true, "optional": true}}}, "uuid": {"version": "3.3.2", "bundled": true, "dev": true}, "validate-npm-package-license": {"version": "3.0.4", "bundled": true, "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "which": {"version": "1.3.1", "bundled": true, "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "bundled": true, "dev": true}, "wordwrap": {"version": "0.0.3", "bundled": true, "dev": true}, "wrap-ansi": {"version": "2.1.0", "bundled": true, "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "bundled": true, "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "bundled": true, "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "bundled": true, "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "bundled": true, "dev": true, "requires": {"ansi-regex": "^2.0.0"}}}}, "wrappy": {"version": "1.0.2", "bundled": true, "dev": true}, "write-file-atomic": {"version": "2.4.2", "bundled": true, "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "y18n": {"version": "4.0.0", "bundled": true, "dev": true}, "yallist": {"version": "2.1.2", "bundled": true, "dev": true}, "yargs": {"version": "12.0.5", "bundled": true, "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^11.1.1"}}, "yargs-parser": {"version": "11.1.1", "bundled": true, "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}, "oauth-sign": {"version": "0.9.0", "resolved": "http://npm.mail.netease.com/registry/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU="}, "object-assign": {"version": "4.1.1", "resolved": "http://npm.mail.netease.com/registry/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-copy": {"version": "0.1.0", "resolved": "http://npm.mail.netease.com/registry/object-copy/download/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-visit": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/object-visit/download/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.pick": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/object.pick/download/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "on-finished": {"version": "2.3.0", "resolved": "http://npm.mail.netease.com/registry/on-finished/download/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "once": {"version": "1.4.0", "resolved": "http://npm.mail.netease.com/registry/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "only": {"version": "0.0.2", "resolved": "http://npm.mail.netease.com/registry/only/download/only-0.0.2.tgz", "integrity": "sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q="}, "ono": {"version": "4.0.11", "resolved": "http://npm.mail.netease.com/registry/ono/download/ono-4.0.11.tgz", "integrity": "sha1-x/Qgmz45bopE70O5ztx/XXkdIh0=", "requires": {"format-util": "^1.0.3"}}, "openapi-schema-validation": {"version": "0.4.2", "resolved": "http://npm.mail.netease.com/registry/openapi-schema-validation/download/openapi-schema-validation-0.4.2.tgz", "integrity": "sha1-iVwpAhvgLgAPccUfhZ2lIRjrHiE=", "requires": {"jsonschema": "1.2.4", "jsonschema-draft4": "^1.0.0", "swagger-schema-official": "2.0.0-bab6bed"}}, "optimist": {"version": "0.6.1", "resolved": "http://npm.mail.netease.com/registry/optimist/download/optimist-0.6.1.tgz", "integrity": "sha1-2j6nRob6IaGaERwybpDrFaAZZoY=", "dev": true, "requires": {"minimist": "~0.0.1", "wordwrap": "~0.0.2"}}, "os-locale": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/os-locale/download/os-locale-3.1.0.tgz", "integrity": "sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=", "requires": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "p-defer": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/p-defer/download/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww="}, "p-finally": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/p-finally/download/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="}, "p-is-promise": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/p-is-promise/download/p-is-promise-2.1.0.tgz", "integrity": "sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4="}, "p-limit": {"version": "2.2.0", "resolved": "http://npm.mail.netease.com/registry/p-limit/download/p-limit-2.2.0.tgz", "integrity": "sha1-QXyZQeYCepq8ulCS3SkE4lW1+8I=", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://npm.mail.netease.com/registry/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "package-json": {"version": "4.0.1", "resolved": "http://npm.mail.netease.com/registry/package-json/download/package-json-4.0.1.tgz", "integrity": "sha1-iGmgQBJTZhxMTKPabCEh7VVfXu0=", "dev": true, "requires": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}}, "parse-json": {"version": "2.2.0", "resolved": "http://npm.mail.netease.com/registry/parse-json/download/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "requires": {"error-ex": "^1.2.0"}}, "parseurl": {"version": "1.3.3", "resolved": "http://npm.mail.netease.com/registry/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "pascalcase": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/pascalcase/download/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/path-dirname/download/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/path-is-inside/download/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "path-parse": {"version": "1.0.6", "resolved": "http://npm.mail.netease.com/registry/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="}, "path-to-regexp": {"version": "1.7.0", "resolved": "http://npm.mail.netease.com/registry/path-to-regexp/download/path-to-regexp-1.7.0.tgz", "integrity": "sha1-Wf3g9DW62suhA6hOnTvGTpa5k30=", "requires": {"isarray": "0.0.1"}}, "path-type": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/path-type/download/path-type-2.0.0.tgz", "integrity": "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=", "dev": true, "requires": {"pify": "^2.0.0"}}, "pathval": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/pathval/download/pathval-1.1.0.tgz", "integrity": "sha1-uULm1L3mUwBe9rcTYd74cn0GReA=", "dev": true}, "performance-now": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns="}, "pify": {"version": "2.3.0", "resolved": "http://npm.mail.netease.com/registry/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "http://npm.mail.netease.com/registry/pinkie/download/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "^2.0.0"}}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://npm.mail.netease.com/registry/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "prepend-http": {"version": "1.0.4", "resolved": "http://npm.mail.netease.com/registry/prepend-http/download/prepend-http-1.0.4.tgz", "integrity": "sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "progress": {"version": "2.0.3", "resolved": "http://npm.mail.netease.com/registry/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=", "dev": true}, "pseudomap": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/pseudomap/download/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="}, "psl": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/psl/download/psl-1.3.0.tgz", "integrity": "sha1-4ev2o7VWT6g3bz2iJ12nbYdcob0="}, "pstree.remy": {"version": "1.1.7", "resolved": "http://npm.mail.netease.com/registry/pstree.remy/download/pstree.remy-1.1.7.tgz", "integrity": "sha1-x2ljooBH7WFULcNhqibuVaf6FfM=", "dev": true}, "pump": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="}, "qs": {"version": "6.7.0", "resolved": "http://npm.mail.netease.com/registry/qs/download/qs-6.7.0.tgz", "integrity": "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="}, "raw-body": {"version": "2.4.1", "resolved": "http://npm.mail.netease.com/registry/raw-body/download/raw-body-2.4.1.tgz", "integrity": "sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "rc": {"version": "1.2.8", "resolved": "http://npm.mail.netease.com/registry/rc/download/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "dev": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "dependencies": {"minimist": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/minimist/download/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}}}, "read-pkg": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/read-pkg/download/read-pkg-2.0.0.tgz", "integrity": "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=", "dev": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}}, "read-pkg-up": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/read-pkg-up/download/read-pkg-up-2.0.0.tgz", "integrity": "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/locate-path/download/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "1.3.0", "resolved": "http://npm.mail.netease.com/registry/p-limit/download/p-limit-1.3.0.tgz", "integrity": "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/p-locate/download/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/p-try/download/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}}}, "readable-stream": {"version": "2.3.6", "resolved": "http://npm.mail.netease.com/registry/readable-stream/download/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}}}, "readdirp": {"version": "2.2.1", "resolved": "http://npm.mail.netease.com/registry/readdirp/download/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}, "rechoir": {"version": "0.6.2", "resolved": "http://npm.mail.netease.com/registry/rechoir/download/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "dev": true, "requires": {"resolve": "^1.1.6"}}, "recursive-iterator": {"version": "3.3.0", "resolved": "http://npm.mail.netease.com/registry/recursive-iterator/download/recursive-iterator-3.3.0.tgz", "integrity": "sha1-TkmM5iJ9jEKy4tSWspa8hmwTRRQ="}, "redis": {"version": "2.8.0", "resolved": "http://npm.mail.netease.com/registry/redis/download/redis-2.8.0.tgz", "integrity": "sha1-ICKI4/WMSfYHnZevehDhMDrhSwI=", "requires": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.6.0"}}, "redis-commands": {"version": "1.5.0", "resolved": "http://npm.mail.netease.com/registry/redis-commands/download/redis-commands-1.5.0.tgz", "integrity": "sha1-gNLiBpj+aI8icSf/nlFkp90X54U="}, "redis-parser": {"version": "2.6.0", "resolved": "http://npm.mail.netease.com/registry/redis-parser/download/redis-parser-2.6.0.tgz", "integrity": "sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs="}, "reflect-metadata": {"version": "0.1.13", "resolved": "http://npm.mail.netease.com/registry/reflect-metadata/download/reflect-metadata-0.1.13.tgz", "integrity": "sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag="}, "regex-not": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/regex-not/download/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "registry-auth-token": {"version": "3.4.0", "resolved": "http://npm.mail.netease.com/registry/registry-auth-token/download/registry-auth-token-3.4.0.tgz", "integrity": "sha1-10RoFUM/XV7WQxzV3KIQSPZrOX4=", "dev": true, "requires": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "registry-url": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/registry-url/download/registry-url-3.1.0.tgz", "integrity": "sha1-PU74cPc93h138M+aOBQyRE4XSUI=", "dev": true, "requires": {"rc": "^1.0.1"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.3", "resolved": "http://npm.mail.netease.com/registry/repeat-element/download/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://npm.mail.netease.com/registry/repeat-string/download/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "request": {"version": "2.88.0", "resolved": "http://npm.mail.netease.com/registry/request/download/request-2.88.0.tgz", "integrity": "sha1-nC/KT301tZLv5Xx/ClXoEFIST+8=", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"form-data": {"version": "2.3.3", "resolved": "http://npm.mail.netease.com/registry/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "qs": {"version": "6.5.2", "resolved": "http://npm.mail.netease.com/registry/qs/download/qs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY="}}}, "request-promise": {"version": "4.2.4", "resolved": "http://npm.mail.netease.com/registry/request-promise/download/request-promise-4.2.4.tgz", "integrity": "sha1-HF7Q1xRB44rVjHzk6k6lsG1UsxA=", "requires": {"bluebird": "^3.5.0", "request-promise-core": "1.1.2", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}}, "request-promise-core": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/request-promise-core/download/request-promise-core-1.1.2.tgz", "integrity": "sha1-M59qq6vK/bMceZ/xWHADNjAdM0Y=", "requires": {"lodash": "^4.17.11"}}, "require-directory": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "require-main-filename": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/require-main-filename/download/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="}, "requires-port": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.12.0", "resolved": "http://npm.mail.netease.com/registry/resolve/download/resolve-1.12.0.tgz", "integrity": "sha1-P8ZEo1yEpIVUYJ/ybsUrZvpXffY=", "requires": {"path-parse": "^1.0.6"}}, "resolve-path": {"version": "1.4.0", "resolved": "http://npm.mail.netease.com/registry/resolve-path/download/resolve-path-1.4.0.tgz", "integrity": "sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=", "requires": {"http-errors": "~1.6.2", "path-is-absolute": "1.0.1"}, "dependencies": {"http-errors": {"version": "1.6.3", "resolved": "http://npm.mail.netease.com/registry/http-errors/download/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "http://npm.mail.netease.com/registry/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "setprototypeof": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/setprototypeof/download/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="}}}, "resolve-url": {"version": "0.2.1", "resolved": "http://npm.mail.netease.com/registry/resolve-url/download/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "ret": {"version": "0.1.15", "resolved": "http://npm.mail.netease.com/registry/ret/download/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true}, "rfdc": {"version": "1.1.4", "resolved": "http://npm.mail.netease.com/registry/rfdc/download/rfdc-1.1.4.tgz", "integrity": "sha1-unLME2egzNnPgahws7WL060H+MI="}, "safe-buffer": {"version": "5.1.2", "resolved": "http://npm.mail.netease.com/registry/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safe-regex": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/safe-regex/download/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "semver": {"version": "5.7.1", "resolved": "http://npm.mail.netease.com/registry/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="}, "semver-diff": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/semver-diff/download/semver-diff-2.1.0.tgz", "integrity": "sha1-S7uEN8jTfksM8aaP1ybsbWRdbTY=", "dev": true, "requires": {"semver": "^5.0.3"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "set-value": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/set-value/download/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "setprototypeof": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/setprototypeof/download/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "shebang-command": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "shelljs": {"version": "0.8.3", "resolved": "http://npm.mail.netease.com/registry/shelljs/download/shelljs-0.8.3.tgz", "integrity": "sha1-p/MxlSDr8J7oEnWyNorbKGZZsJc=", "dev": true, "requires": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}}, "shimmer": {"version": "1.2.1", "resolved": "http://npm.mail.netease.com/registry/shimmer/download/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc="}, "signal-exit": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/signal-exit/download/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0="}, "snapdragon": {"version": "0.8.2", "resolved": "http://npm.mail.netease.com/registry/snapdragon/download/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "source-map": {"version": "0.5.7", "resolved": "http://npm.mail.netease.com/registry/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-resolve": {"version": "0.5.2", "resolved": "http://npm.mail.netease.com/registry/source-map-resolve/download/source-map-resolve-0.5.2.tgz", "integrity": "sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=", "dev": true, "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.13", "resolved": "http://npm.mail.netease.com/registry/source-map-support/download/source-map-support-0.5.13.tgz", "integrity": "sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://npm.mail.netease.com/registry/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "source-map-url": {"version": "0.4.0", "resolved": "http://npm.mail.netease.com/registry/source-map-url/download/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "spdx-correct": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/spdx-correct/download/spdx-correct-3.1.0.tgz", "integrity": "sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.2.0", "resolved": "http://npm.mail.netease.com/registry/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz", "integrity": "sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=", "dev": true}, "spdx-expression-parse": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz", "integrity": "sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.5", "resolved": "http://npm.mail.netease.com/registry/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz", "integrity": "sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/split-string/download/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "sshpk": {"version": "1.16.1", "resolved": "http://npm.mail.netease.com/registry/sshpk/download/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "static-extend": {"version": "0.1.2", "resolved": "http://npm.mail.netease.com/registry/static-extend/download/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://npm.mail.netease.com/registry/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.5.0", "resolved": "http://npm.mail.netease.com/registry/statuses/download/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "stealthy-require": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/stealthy-require/download/stealthy-require-1.1.1.tgz", "integrity": "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks="}, "streamroller": {"version": "1.0.6", "resolved": "http://npm.mail.netease.com/registry/streamroller/download/streamroller-1.0.6.tgz", "integrity": "sha1-gWfYSW7Z8Z8F7ksVjZYRMhuMrNk=", "requires": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.14"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "string-width": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://npm.mail.netease.com/registry/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/strip-bom/download/strip-bom-3.0.0.tgz", "integrity": "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=", "dev": true}, "strip-eof": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="}, "strip-json-comments": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "dev": true}, "superagent": {"version": "3.8.3", "resolved": "http://npm.mail.netease.com/registry/superagent/download/superagent-3.8.3.tgz", "integrity": "sha1-Rg6g29t9WxG8T3jeulZfhqF44Sg=", "dev": true, "requires": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "dev": true, "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "supertest": {"version": "3.4.2", "resolved": "http://npm.mail.netease.com/registry/supertest/download/supertest-3.4.2.tgz", "integrity": "sha1-utfeLkPWDSfIyuuKs0pnyKX3Gq0=", "dev": true, "requires": {"methods": "^1.1.2", "superagent": "^3.8.3"}}, "supports-color": {"version": "5.5.0", "resolved": "http://npm.mail.netease.com/registry/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}, "swagger-methods": {"version": "1.0.8", "resolved": "http://npm.mail.netease.com/registry/swagger-methods/download/swagger-methods-1.0.8.tgz", "integrity": "sha1-i6837oYdPHL/ey+q1tdMYLM24u0="}, "swagger-parser": {"version": "5.0.6", "resolved": "http://npm.mail.netease.com/registry/swagger-parser/download/swagger-parser-5.0.6.tgz", "integrity": "sha1-37hwJPjJyMa7fBOQ6zz9Bl50RA4=", "requires": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "json-schema-ref-parser": "^5.1.3", "ono": "^4.0.6", "openapi-schema-validation": "^0.4.2", "swagger-methods": "^1.0.4", "swagger-schema-official": "2.0.0-bab6bed", "z-schema": "^3.23.0"}, "dependencies": {"debug": {"version": "3.2.6", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "swagger-schema-official": {"version": "2.0.0-bab6bed", "resolved": "http://npm.mail.netease.com/registry/swagger-schema-official/download/swagger-schema-official-2.0.0-bab6bed.tgz", "integrity": "sha1-cAcEaNbSl3ylI3suUZyn0Gouo/0="}, "term-size": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/term-size/download/term-size-1.2.0.tgz", "integrity": "sha1-RYuDiH8oj8Vtb/+/rSYuJmOO+mk=", "dev": true, "requires": {"execa": "^0.7.0"}, "dependencies": {"cross-spawn": {"version": "5.1.0", "resolved": "http://npm.mail.netease.com/registry/cross-spawn/download/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "requires": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "execa": {"version": "0.7.0", "resolved": "http://npm.mail.netease.com/registry/execa/download/execa-0.7.0.tgz", "integrity": "sha1-lEvs00zEHuMqY6n68nrVpl/Fl3c=", "dev": true, "requires": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "get-stream": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true}}}, "thenify": {"version": "3.3.0", "resolved": "http://npm.mail.netease.com/registry/thenify/download/thenify-3.3.0.tgz", "integrity": "sha1-5p44obq+lpsBCCB5eLn2K4hgSDk=", "requires": {"any-promise": "^1.0.0"}}, "thenify-all": {"version": "1.6.0", "resolved": "http://npm.mail.netease.com/registry/thenify-all/download/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "requires": {"thenify": ">= 3.1.0 < 4"}}, "timed-out": {"version": "4.0.1", "resolved": "http://npm.mail.netease.com/registry/timed-out/download/timed-out-4.0.1.tgz", "integrity": "sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "http://npm.mail.netease.com/registry/to-object-path/download/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"is-buffer": {"version": "1.1.6", "resolved": "http://npm.mail.netease.com/registry/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "kind-of": {"version": "3.2.2", "resolved": "http://npm.mail.netease.com/registry/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/to-regex/download/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/to-regex-range/download/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "toidentifier": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/toidentifier/download/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "topo": {"version": "3.0.3", "resolved": "http://npm.mail.netease.com/registry/topo/download/topo-3.0.3.tgz", "integrity": "sha1-1aZ/suaTB+vusIQC7Coqb1962Vw=", "requires": {"hoek": "6.x.x"}}, "touch": {"version": "3.1.0", "resolved": "http://npm.mail.netease.com/registry/touch/download/touch-3.1.0.tgz", "integrity": "sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=", "dev": true, "requires": {"nopt": "~1.0.10"}}, "tough-cookie": {"version": "2.4.3", "resolved": "http://npm.mail.netease.com/registry/tough-cookie/download/tough-cookie-2.4.3.tgz", "integrity": "sha1-U/Nto/R3g7CSWvoG/587FlKA94E=", "requires": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "http://npm.mail.netease.com/registry/punycode/download/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4="}}}, "trim-right": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/trim-right/download/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "ts-node": {"version": "6.2.0", "resolved": "http://npm.mail.netease.com/registry/ts-node/download/ts-node-6.2.0.tgz", "integrity": "sha1-ZaCuKszjGepP16yNfJ8fkMXaa68=", "dev": true, "requires": {"arrify": "^1.0.0", "buffer-from": "^1.1.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.6", "yn": "^2.0.0"}, "dependencies": {"minimist": {"version": "1.2.0", "resolved": "http://npm.mail.netease.com/registry/minimist/download/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}}}, "tslib": {"version": "1.10.0", "resolved": "http://npm.mail.netease.com/registry/tslib/download/tslib-1.10.0.tgz", "integrity": "sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo="}, "tslint": {"version": "5.18.0", "resolved": "http://npm.mail.netease.com/registry/tslint/download/tslint-5.18.0.tgz", "integrity": "sha1-9hpt3PNyNErF5BcICVu/BDoUesY=", "requires": {"@babel/code-frame": "^7.0.0", "builtin-modules": "^1.1.1", "chalk": "^2.3.0", "commander": "^2.12.1", "diff": "^3.2.0", "glob": "^7.1.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "resolve": "^1.3.2", "semver": "^5.3.0", "tslib": "^1.8.0", "tsutils": "^2.29.0"}}, "tsutils": {"version": "2.29.0", "resolved": "http://npm.mail.netease.com/registry/tsutils/download/tsutils-2.29.0.tgz", "integrity": "sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k=", "requires": {"tslib": "^1.8.1"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://npm.mail.netease.com/registry/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://npm.mail.netease.com/registry/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q="}, "type-detect": {"version": "4.0.8", "resolved": "http://npm.mail.netease.com/registry/type-detect/download/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true}, "type-is": {"version": "1.6.18", "resolved": "http://npm.mail.netease.com/registry/type-is/download/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typedoc": {"version": "0.13.0", "resolved": "http://npm.mail.netease.com/registry/typedoc/download/typedoc-0.13.0.tgz", "integrity": "sha1-nv3zUr1UhzlVzRYb1LdfJKjFnd4=", "dev": true, "requires": {"@types/fs-extra": "^5.0.3", "@types/handlebars": "^4.0.38", "@types/highlight.js": "^9.12.3", "@types/lodash": "^4.14.110", "@types/marked": "^0.4.0", "@types/minimatch": "3.0.3", "@types/shelljs": "^0.8.0", "fs-extra": "^7.0.0", "handlebars": "^4.0.6", "highlight.js": "^9.0.0", "lodash": "^4.17.10", "marked": "^0.4.0", "minimatch": "^3.0.0", "progress": "^2.0.0", "shelljs": "^0.8.2", "typedoc-default-themes": "^0.5.0", "typescript": "3.1.x"}, "dependencies": {"typescript": {"version": "3.1.6", "resolved": "http://npm.mail.netease.com/registry/typescript/download/typescript-3.1.6.tgz", "integrity": "sha1-tlQ6g8/Iwr77P0yPumiW9bDJvmg=", "dev": true}}}, "typedoc-default-themes": {"version": "0.5.0", "resolved": "http://npm.mail.netease.com/registry/typedoc-default-themes/download/typedoc-default-themes-0.5.0.tgz", "integrity": "sha1-bcJDPnjti+qOiHo6zeLzF4W9Yic=", "dev": true}, "typescript": {"version": "3.5.3", "resolved": "http://npm.mail.netease.com/registry/typescript/download/typescript-3.5.3.tgz", "integrity": "sha1-yDD2V/k/HqhGgZ6SkJL1/lmD6Xc="}, "typescript-json-schema": {"version": "0.36.0", "resolved": "http://npm.mail.netease.com/registry/typescript-json-schema/download/typescript-json-schema-0.36.0.tgz", "integrity": "sha1-CHSKOfzCPZKIGaIPOU/kwPm4hjk=", "requires": {"glob": "~7.1.2", "json-stable-stringify": "^1.0.1", "typescript": "^3.0.1", "yargs": "^12.0.1"}, "dependencies": {"yargs": {"version": "12.0.5", "resolved": "http://npm.mail.netease.com/registry/yargs/download/yargs-12.0.5.tgz", "integrity": "sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM=", "requires": {"cliui": "^4.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^11.1.1"}}}}, "uglify-js": {"version": "3.6.0", "resolved": "http://npm.mail.netease.com/registry/uglify-js/download/uglify-js-3.6.0.tgz", "integrity": "sha1-cEaBNFxTqLIHn7bOwpSwXq0kL/U=", "dev": true, "optional": true, "requires": {"commander": "~2.20.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://npm.mail.netease.com/registry/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "optional": true}}}, "undefsafe": {"version": "2.0.2", "resolved": "http://npm.mail.netease.com/registry/undefsafe/download/undefsafe-2.0.2.tgz", "integrity": "sha1-Il9rngM3Zj4Njnz9aG/Cg2zKznY=", "dev": true, "requires": {"debug": "^2.2.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://npm.mail.netease.com/registry/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}}}, "union-value": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/union-value/download/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unique-string": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/unique-string/download/unique-string-1.0.0.tgz", "integrity": "sha1-nhBXzKhRq7kzmPizOuGHuZyuwRo=", "dev": true, "requires": {"crypto-random-string": "^1.0.0"}}, "universalify": {"version": "0.1.2", "resolved": "http://npm.mail.netease.com/registry/universalify/download/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="}, "unpipe": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "unset-value": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/unset-value/download/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://npm.mail.netease.com/registry/has-value/download/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/isobject/download/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://npm.mail.netease.com/registry/has-values/download/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}}}, "unzip-response": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/unzip-response/download/unzip-response-2.0.1.tgz", "integrity": "sha1-0vD3N9FrBhXnKmk17QQhRXLVb5c=", "dev": true}, "upath": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/upath/download/upath-1.1.2.tgz", "integrity": "sha1-PbZYYA7a7sy+bbXmhNZ+6MKs0Gg=", "dev": true}, "update-notifier": {"version": "2.5.0", "resolved": "http://npm.mail.netease.com/registry/update-notifier/download/update-notifier-2.5.0.tgz", "integrity": "sha1-0HRFk+E/Fh5AassdlAi3LK0Ir/Y=", "dev": true, "requires": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-ci": "^1.0.10", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}}, "uri-js": {"version": "4.2.2", "resolved": "http://npm.mail.netease.com/registry/uri-js/download/uri-js-4.2.2.tgz", "integrity": "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=", "requires": {"punycode": "^2.1.0"}}, "urijs": {"version": "1.19.1", "resolved": "http://npm.mail.netease.com/registry/urijs/download/urijs-1.19.1.tgz", "integrity": "sha1-Ww/1MMDL3oOG9jQiNbpcpumV0lo="}, "urix": {"version": "0.1.0", "resolved": "http://npm.mail.netease.com/registry/urix/download/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url-parse-lax": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/url-parse-lax/download/url-parse-lax-1.0.0.tgz", "integrity": "sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=", "dev": true, "requires": {"prepend-http": "^1.0.1"}}, "use": {"version": "3.1.1", "resolved": "http://npm.mail.netease.com/registry/use/download/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true}, "util-deprecate": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.3.2", "resolved": "http://npm.mail.netease.com/registry/uuid/download/uuid-3.3.2.tgz", "integrity": "sha1-G0r0lV6zB3xQHCOHL8ZROBFYcTE="}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://npm.mail.netease.com/registry/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "validator": {"version": "10.11.0", "resolved": "http://npm.mail.netease.com/registry/validator/download/validator-10.11.0.tgz", "integrity": "sha1-ADEI6m6amHTTHMyeUAaFbM12sig="}, "vary": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "verror": {"version": "1.10.0", "resolved": "http://npm.mail.netease.com/registry/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "which": {"version": "1.3.1", "resolved": "http://npm.mail.netease.com/registry/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="}, "widest-line": {"version": "2.0.1", "resolved": "http://npm.mail.netease.com/registry/widest-line/download/widest-line-2.0.1.tgz", "integrity": "sha1-dDh2RzDsfvQ4HOTfgvuYpTFCo/w=", "dev": true, "requires": {"string-width": "^2.1.1"}}, "wordwrap": {"version": "0.0.3", "resolved": "http://npm.mail.netease.com/registry/wordwrap/download/wordwrap-0.0.3.tgz", "integrity": "sha1-o9XabNXAvAAI03I0u68b7WMFkQc=", "dev": true}, "wrap-ansi": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/wrap-ansi/download/wrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "requires": {"number-is-nan": "^1.0.0"}}, "string-width": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/string-width/download/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}}}, "wrappy": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write-file-atomic": {"version": "2.4.3", "resolved": "http://npm.mail.netease.com/registry/write-file-atomic/download/write-file-atomic-2.4.3.tgz", "integrity": "sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE=", "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "xdg-basedir": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/xdg-basedir/download/xdg-basedir-3.0.0.tgz", "integrity": "sha1-SWsswQnsqNus/i3HK2A8F8WHCtQ=", "dev": true}, "y18n": {"version": "4.0.0", "resolved": "http://npm.mail.netease.com/registry/y18n/download/y18n-4.0.0.tgz", "integrity": "sha1-le+U+F7MgdAHwmThkKEg8KPIVms="}, "yallist": {"version": "2.1.2", "resolved": "http://npm.mail.netease.com/registry/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="}, "yargs": {"version": "7.1.0", "resolved": "http://npm.mail.netease.com/registry/yargs/download/yargs-7.1.0.tgz", "integrity": "sha1-a6MY6xaWFyf10oT46gA+jWFU0Mg=", "dev": true, "requires": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "http://npm.mail.netease.com/registry/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "camelcase": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/camelcase/download/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo=", "dev": true}, "cliui": {"version": "3.2.0", "resolved": "http://npm.mail.netease.com/registry/cliui/download/cliui-3.2.0.tgz", "integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "find-up": {"version": "1.1.2", "resolved": "http://npm.mail.netease.com/registry/find-up/download/find-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "invert-kv": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/invert-kv/download/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "lcid": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/lcid/download/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "load-json-file": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/load-json-file/download/load-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "os-locale": {"version": "1.4.0", "resolved": "http://npm.mail.netease.com/registry/os-locale/download/os-locale-1.4.0.tgz", "integrity": "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=", "dev": true, "requires": {"lcid": "^1.0.0"}}, "path-exists": {"version": "2.1.0", "resolved": "http://npm.mail.netease.com/registry/path-exists/download/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "dev": true, "requires": {"pinkie-promise": "^2.0.0"}}, "path-type": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/path-type/download/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "read-pkg": {"version": "1.1.0", "resolved": "http://npm.mail.netease.com/registry/read-pkg/download/read-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "dev": true, "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "resolved": "http://npm.mail.netease.com/registry/read-pkg-up/download/read-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "dev": true, "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}}, "string-width": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/string-width/download/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/strip-bom/download/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "requires": {"is-utf8": "^0.2.0"}}, "which-module": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/which-module/download/which-module-1.0.0.tgz", "integrity": "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8=", "dev": true}, "y18n": {"version": "3.2.1", "resolved": "http://npm.mail.netease.com/registry/y18n/download/y18n-3.2.1.tgz", "integrity": "sha1-bRX7qITAhnnA136I53WegR4H+kE=", "dev": true}, "yargs-parser": {"version": "5.0.0", "resolved": "http://npm.mail.netease.com/registry/yargs-parser/download/yargs-parser-5.0.0.tgz", "integrity": "sha1-J17PDX/+Bcd+ZOfIbkzZS/DhIoo=", "dev": true, "requires": {"camelcase": "^3.0.0"}}}}, "yargs-parser": {"version": "11.1.1", "resolved": "http://npm.mail.netease.com/registry/yargs-parser/download/yargs-parser-11.1.1.tgz", "integrity": "sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ=", "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "ylru": {"version": "1.2.1", "resolved": "http://npm.mail.netease.com/registry/ylru/download/ylru-1.2.1.tgz", "integrity": "sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8="}, "yn": {"version": "2.0.0", "resolved": "http://npm.mail.netease.com/registry/yn/download/yn-2.0.0.tgz", "integrity": "sha1-5a2ryKz0CPY4X8dklWhMiOavaJo=", "dev": true}, "z-schema": {"version": "3.25.1", "resolved": "http://npm.mail.netease.com/registry/z-schema/download/z-schema-3.25.1.tgz", "integrity": "sha1-fhRmO+K5YAPZOKVvZE+4VhZD+34=", "requires": {"commander": "^2.7.1", "core-js": "^2.5.7", "lodash.get": "^4.0.0", "lodash.isequal": "^4.0.0", "validator": "^10.0.0"}}}}