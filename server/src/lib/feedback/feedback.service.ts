import axios from '@tiger/request';
import { Service } from '@tiger/boot';
import { ConfigService } from './conf/config.service';
import { AppConfig, AjaxSearchResult, AjaxResult } from '@tiger/core';
import { FeedbackQueryVO, FeedbackVO, SaveFeedBackVO } from './vo/feed-back.vo';

@Service
export class FeedbackService{
    constructor(private configService: ConfigService){

    }

    /**
     * 分页查询反馈意见
     * @author: 金炳
     * @data: 2018-11-02 14:24:51
     */
    async pagingSearch(currentUid: string, feedbackQueryVo: FeedbackQueryVO): Promise<AjaxSearchResult<FeedbackVO>>{
        const url = this.configService.serverUrl + '/xhr/feedback/' + AppConfig.get('productCode') + '/pagingSearch.json?uid=' + currentUid;
        return (await axios.post<AjaxSearchResult<FeedbackVO>>(url, feedbackQueryVo)).data;
    }

    /**
     * 保存反馈意见
     * @author: 金炳
     * @blog: https://www.520stone.com
     * @data: 2018-11-02 14:42:55
     */
    async saveFeedBack(currentUid: string, saveFeedback: SaveFeedBackVO){
        const url = this.configService.serverUrl + '/xhr/feedback/' +  AppConfig.get('productCode') + '/saveFeedBack.json?uid=' + currentUid;
        return (await axios.post<AjaxResult<any>>(url, saveFeedback)).data;
    }
}
