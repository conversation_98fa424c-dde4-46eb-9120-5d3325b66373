import { Service } from '@tiger/boot';
import { getServiceUrl } from '@tiger/core';

@Service
export class ConfigService{

    /**
     * 各种环境的接口
     * @author: 金炳
     * @data: 2018-11-02 11:06:52
     */
    feedbackServices: any = {
        dev: 'http://owlweb.you.163.com',
        test: 'http://127.0.0.1:8550/proxy/test.yanxuan-owl-web.service.mailsaas',
        online: 'http://127.0.0.1:8550/proxy/online.yanxuan-owl-web.service.mailsaas'
    };

    get serverUrl(){
        return getServiceUrl(this.feedbackServices);
    }
}
