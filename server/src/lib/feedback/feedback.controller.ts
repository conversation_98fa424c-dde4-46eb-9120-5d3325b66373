import { RequestMapping, RestController, PostMapping } from '@tiger/boot';
import { RequestContext, AjaxSearchResult, AjaxResult } from '@tiger/core';
import { FeedbackQueryVO, FeedbackVO, SaveFeedBackVO } from './vo/feed-back.vo';
import { FeedbackService } from './feedback.service';

/**
 * 反馈模块
 * @author: 金炳
 * @data: 2018-11-02 10:38:53
 */
@RestController
@RequestMapping('/xhr/eagle/feedback')
export class FeedBackController {

    constructor(private feedbackService: FeedbackService) {

    }

    /**
     * 获取反馈列表
     * @author: 金炳
     * @data: 2018-11-02 10:36:34
     */
    @PostMapping('/pagingSearch.json')
    public async list(ctx: RequestContext<FeedbackQueryVO, AjaxSearchResult<FeedbackVO>>) {
        if (!ctx.request.body) {
            return ctx.body = AjaxResult.badRequest('参数不存在');
        }
        const result: AjaxSearchResult<FeedbackVO> = await this.feedbackService.pagingSearch(ctx.openIDInfo.email, ctx.request.body);
        ctx.body = result;
    }

    /**
     * 发起一个反馈
     * @author: 金炳
     * @data: 2018-11-02 10:40:28
     */
    @PostMapping('/saveFeedBack.json')
    public async post(ctx: RequestContext<SaveFeedBackVO, AjaxResult<any>>) {
        if (!ctx.request.body) {
            return ctx.body = AjaxResult.badRequest('参数不存在');
        }
        await this.feedbackService.saveFeedBack(ctx.openIDInfo.email, ctx.request.body);
        ctx.body = AjaxResult.success();
    }
}
