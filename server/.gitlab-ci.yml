stages:
  - install_dependent
  - tslint
  - test

cache:
  key: "$CI_PROJECT_NAME"
  paths:
    - node_modules

install_dependent:
  stage: install_dependent
  script:
    - npm install --registry http://npm.hz.infra.mail/registry/
  tags:
    - docker-runner

tslint:
  stage: tslint
  script:
    - npm run tslint
  tags:
    - docker-runner

test:
  stage: test
  script:
    - npm run test
  tags:
    - docker-runner
