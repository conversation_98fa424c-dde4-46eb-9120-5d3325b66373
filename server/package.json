{"name": "react-template-server", "version": "0.0.1", "description": "common services", "main": "./src/lib/index.js", "types": "./src/lib/index.d.ts", "scripts": {"nyc": "nyc --reporter=lcov npm test", "test": "npm run clean && tsc && jasmine-ts --config=spec/support/jasmine.json", "server": "nodemon --config ./nodemon.json ./app/index.ts", "package": "npm run clean && tsc -P ./tsconfig.json && cp package.json publish && cp -rf docs publish", "clean": "rm -rf publish coverage", "deploy": "npm run package && cd publish && ppnpm publish", "tslint": "tslint -c tslint.json 'src/**/*.ts' -p ./tsconfig.json"}, "repository": {"type": "git", "url": "https://git.mail.netease.com/eagle/common-service-node"}, "author": "yangbo", "license": "MIT", "dependencies": {"@eagle/common-service-node": "^3.0.3", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "debug": "^4.1.0", "request": "^2.88.0", "request-promise": "^4.2.2", "@eagle/workflow-node": "^4.0.0", "@tiger/apolloy": "^4.0.0", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.0.0", "@tiger/ejs": "^4.0.0", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.0", "@tiger/info": "^4.0.0", "@tiger/logger": "^4.0.0", "@tiger/monitor": "^4.0.42", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/security": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/validator": "^4.0.0", "boom": "^7.1.1", "koa": "^2.5.3", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "path-to-regexp": "^1.1.1"}, "devDependencies": {"@eagle/workflow-node": "^3.0.0", "@tiger/ejs": "^3.0.2", "@tiger/error": "^3.0.0", "@tiger/health": "^3.0.0", "@tiger/permission": "^3.0.2", "@tiger/security": "^3.0.0", "@tiger/session": "^3.0.0", "@tiger/tslint": "^1.0.0", "@tiger/validator": "^3.0.2", "@types/debug": "latest", "@types/jasmine": "^2.8.9", "install": "^0.12.2", "joi": "^14.3.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "npm": "^6.9.0", "nyc": "^13.1.0", "reflect-metadata": "^0.1.13", "typedoc": "^0.13.0", "@types/boom": "^7.2.0", "@types/chai": "^4.1.7", "@types/ejs": "^2.6.3", "@types/joi": "^14.3.2", "@types/koa": "^2.0.43", "@types/koa-etag": "^3.0.0", "@types/koa-router": "7.0.35", "@types/koa-send": "^4.1.1", "@types/node": "^8.10.29", "@types/supertest": "^2.0.6", "@vscode-snippets/tiger": "^1.0.0", "chai": "^4.2.0", "fs-extra": "^7.0.0", "get-port": "^3.2.0", "jasmine": "^3.3.1", "jasmine-ts": "^0.3.0", "nodemon": "^1.14.7", "supertest": "^3.3.0", "ts-node": "^6.1.1", "tslint": "^5.12.1", "typescript": "^3.0.0", "yargs": "^7.0.2"}}