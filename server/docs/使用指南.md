

----
title: 快速入门
data: 2018-09-16 21:00:00
----

## 快速入门
本文将从实例的角度，一步步地快速搭建出一个Tiger应用，让你快速的入门Tiger。

## 环境准备
* 操作系统: 支持macOS,Linux, Windows
* 运行环境: 安装typescript

## 快速初始化
我们推荐直接使用脚手架，只需几条简单命令，即可快速生成项目:
```plain
$ npm i @tiger/init -g
$ tiger-init tiger-example --type=simple
$ cd tiger-example
$ npm i 
```
启动项目:
```plain
$ npm run dev
$ open localhost:7002/swagger
```

## 项目介绍
项目是使用typescript进行开发，Tiger整体是在Koa2和Koa Router之上做了封装，使写后端应用的时候，更加的方便。同时默认集成swagger方便本地调试开发。
## 效果图

![image.png | left | 827x347](https://cdn.nlark.com/yuque/0/2018/png/187105/1540259226137-d55cd102-45b1-4dc7-b73d-4d630632d600.png "")

然后我们可以通过swagger中的try it out来方便本地的调试开发。

## 编写一个模块
Tiger的思维按照模块进行划分，当然也可以按照java的按类型进行划分两种情况。

### 按照模块划分
modules文件夹下面的都是一个个module，然后每一个module的结构:

![image.png | left | 451x263](https://cdn.nlark.com/yuque/0/2018/png/187105/1540259532629-6183063c-7a91-4ff1-b300-ceac9f159724.png "")

然后每次我们要新建一个模块的接口，那我们就在modules文件夹下面，新建一个模块的文件夹，然后新建xxx.controller.ts文件，然后controller.ts文件中的写法:
```
@RestController
@RequestMapping("/xhr")
export class **Controller
{
    @GetMapping("/helloworld.do")
    public helloworld(ctx:Context){
        ctx.body = 'hello world';
    }
}
```
我们再在app.ts文件中，
```
ScanController中，追加我们的Controller，这个Controller的顺序，跟原先koa里面一样，谁放在前面就执行谁的。
```
然后刷新swagger的页面，可以看到新的接口了，然后我们可以通过界面进行调试了。
如果我们没有service和vo文件则我们的目录结构就是如下：
```plain
|____user
| |____user.controller.ts
```
如果我们有service和vo等文件的话，则我们的目录结构就是如下:
```plain
|____user
| |____vo
| | |____queryData.vo.ts
| | |____user.vo.ts
| |____user.controller.ts
| |____user.service.ts
```
### 按照类型划分
那我们把modules文件夹拆成，controller文件夹，service文件夹，vo文件夹
结构如下:
```plain
.
|____app.ts
|____vo
| |____IUser.vo.ts
|____controller
| |____user.controller.ts
|____service
| |____user.service.ts
```
这个分层，基本跟spring、springboot那边的一样，所以大家根据自己习惯进行选择，每个自己的团队尽量按照一个风格。优先推荐按照模块划分，因为把一个module发成一个npm包，就能让不同的后端拥有相同一个功能的模块了。

