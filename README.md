## 目录说明

- web 为前端的业务组件包的开发目录
- server 为后端的业务组件包的开发目录

## 开发方式
### 1. 前端运行方式

```bash
$ cd web && npm run dev

配置host：
127.0.0.1 local.yx.mail.netease.com
127.0.0.1 remote.yx.mail.netease.com

- 本地mock开发 访问local.yx.mail.netease.com
- 联调模式 访问remote.yx.mail.netease.com
```

### 2. 后端运行方式

```bash
$ cd server && npm run server
```

### 3. 打包-本地包

```bash
$ npm run build
```

### 4. 打包-测试包

```bash
$ npm run build:test
```

### 5. 打包-线上包

```bash
$ npm run build:online
```
