/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs');
const path = require('path');
const opn = require('opn');
const chalk = require('chalk');
const getPort = require('get-port');

//webpack 配置
const sharkrScripts = require('@sharkr/scripts');

const sw = new sharkrScripts();
const webpack = sw.getModule('webpack');
const sharkConf = sw.config;
const webpackConfig = sw.getDevConfig();

//use koa
const koa = require('koa');
const koaBody = require('koa-body');
const koaWebpackMiddleware = require('koa-webpack-middleware');
const koaProxies = require('koa-proxies');

const app = new koa();

const compiler = webpack(webpackConfig);
const devMiddleware = koaWebpackMiddleware.devMiddleware(compiler, {
    publicPath: webpackConfig.output.publicPath,
    logTime: true,
    stats: {
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false,
        cachedAssets: false,
    },
});
app.use(async (ctx, next) => {
    const r1 = new RegExp(new RegExp(sharkConf.contextPath + '(/[\\w,-]+)*/$')); // /\/micro-fed(\/\S+)*\/$/
    const r2 = /\/\w+.html$/;
    let redirectHtml;
    if (r1.test(ctx.path)) {
        redirectHtml = '/index.html';
    } else {
        const matchList = ctx.path.match(r2);
        if (matchList) {
            redirectHtml = matchList[0];
        }
    }
    if (redirectHtml) {
        ctx.req.url = redirectHtml;
    }
    await devMiddleware(ctx, next);
});
app.use(koaWebpackMiddleware.hotMiddleware(compiler));

//根据host区分转发
app.use(async (ctx, next) => {
    // remote模式
    if (ctx.host.indexOf('remote.yx.mail.netease.com') > -1) {
        koaProxies(`${sharkConf.contextPath}*${sharkConf.xhrPrefix}`, {
            target: sharkConf.remote.url,
            changeOrigin: true,
            logs: true,
        })(ctx, next);
    } else {
        // local模式
        await next();
    }
});
// 权限中心与人员选择器的转发
app.use(
    koaProxies(`${sharkConf.contextPath}*/xhr/userCenterManage`, {
        target: 'http://yxius.you.163.com',
        changeOrigin: true,
        rewrite: path => path.replace(`${sharkConf.contextPath}/xhr/userCenterManage`, ''),
        logs: true,
    })
);
// icac转发
app.use(
    koaProxies('/icac', {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        logs: true,
    })
);
app.use(koaBody({ textLimit: '100mb', jsonLimit: '100mb', formLimit: '100mb' }));
//本地mock
app.use(async (ctx, next) => {
    const reg = new RegExp(`${sharkConf.xhrPrefix}`);
    if (!reg.test(ctx.path)) {
        await next();
    } else {
        const emitContentPath = ctx.path.replace(`${sharkConf.contextPath}/`, '');
        const mockFilePath = path.join(sharkConf.appMock, emitContentPath);
        console.log('mock data->', mockFilePath);
        if (fs.existsSync(mockFilePath)) {
            ctx.set('Content-Type', 'application/json; charset=UTF-8');
            ctx.body = fs.readFileSync(mockFilePath, 'utf-8');
        } else {
            await next();
        }
    }
});

//not found
app.use(async ctx => {
    ctx.status = 404;
    ctx.body = {
        code: 404,
    };
});

//open url
getPort({ port: getPort.makeRange(sharkConf.port, sharkConf.port + 100) })
    .then(port => {
        const clientUrl = `http://local.yx.mail.netease.com:${port}${sharkConf.contextPath}/index.html`;
        devMiddleware.waitUntilValid(() => {
            console.log(
                chalk.green('\nLive Development Server is listening on '),
                chalk.blue.underline(clientUrl)
            );
            opn(clientUrl);
        });
        app.listen(port);
    })
    .catch(err => {
        console.error(err);
    });
