/* eslint-disable @typescript-eslint/no-var-requires */
const { argv } = require('yargs');
//webpack 配置
const sharkrScripts = require('@sharkr/scripts');

const sw = new sharkrScripts();
const webpackConfig = sw.getBuildConfig(argv.target);

// 在此处可以配置 build 过程中需要外接的 config 文件

sw.runBuild({
    webpackConfig: webpackConfig, // optional
    target: argv.target, // required
    callback: () => {}, // optional
});
