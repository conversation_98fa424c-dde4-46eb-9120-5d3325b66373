/* eslint-disable no-console */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable prefer-destructuring */
const execSync = require('child_process').execSync;
const path = require('path');
const fs = require('fs');
const os = require('os');
const fse = require('fs-extra');

const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

const packageJson = require(resolveApp('./package.json'));

console.log('------ empty publish dir ------');
fse.emptyDirSync('./publish'); //清空publish目录

try {
    console.log('------ tsc ------');
    execSync(`tsc -p ${resolveApp('./tsconfig.npm.json')}`, {encoding: 'utf-8'});
} catch (error) {
    console.log(error);
}

console.log('------ copy files to publish dir ------');
fse.copySync(resolveApp('./src/lib'), resolveApp('./publish'), {
    filter: (src, dest) => {
        if ((!/\.ts$/.test(src) && !/\.tsx$/.test(src)) || /\.d.ts$/.test(src)) {
            return true;
        }
        return false;
    },
});

try {
    console.log('------ build ------');
    execSync('npm run build');
} catch (error) {
     console.log(error);
}

console.log('------ copy package.json ------');
fs.writeFileSync(
    resolveApp('./publish/package.json'),
    JSON.stringify(packageJson, null, 2) + os.EOL
);

// copy client folder
console.log('------ copy docs files ------');
if (fse.existsSync(resolveApp('./build/client'))) {
    fse.copySync(resolveApp('./build/client'), resolveApp('./publish/docs/client'));
}

if (fse.existsSync(resolveApp('./mock'))) {
    fse.copySync(resolveApp('./mock'), resolveApp('./publish/docs/mock'));
}

// copy description.json
console.log('------ copy description.json ------');
if (fse.existsSync(resolveApp('./description.json'))) {
    fse.copySync(resolveApp('./description.json'), resolveApp('./publish/docs/description.json'));
}
