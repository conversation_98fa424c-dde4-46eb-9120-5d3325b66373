{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "declaration": true, "allowJs": true, "sourceMap": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "strictNullChecks": false, "isolatedModules": true, "jsx": "react", "outDir": "publish"}, "include": ["src/lib"]}