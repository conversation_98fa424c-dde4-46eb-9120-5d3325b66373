module.exports = {
	extends: ['@sharkr/eslint-config-react/typescript'],
	rules: {
		'import/no-webpack-loader-syntax': 0, // 不可使用loader
		'@typescript-eslint/no-var-requires': 0, // 不可使用require
		'react/self-closing-comp': 'off',
		'prettier/prettier': 'off',
		'jsx-quotes': 'off',
		'prefer-promise-reject-errors': 'off',
		'no-return-await': 'off',
		'react/state-in-constructor': 'off',
		'react/jsx-sort-props': 'off',
		'react/jsx-one-expression-per-line': 'off',
		'react/jsx-props-no-spreading': 'off',
		'@typescript-eslint/interface-name-prefix': 'off',
		'react/jsx-no-bind': 'off',
		'import/no-default-export': 'off',
		'react/prop-types': 'off',
		'no-debugger': 'off',
		'no-extra-boolean-cast': 'off',
		'one-var': 'off',
		'id-length': 'off',
		'react/jsx-closing-bracket-location': 'off',
		complexity: ['error', 40],
		'no-param-reassign': 'off',
		'react/jsx-boolean-value': 'off',
		'no-multi-spaces': 'off',
		'guard-for-in': 'off',
		'react/jsx-closing-tag-location': 'off',
		'react/jsx-closing-bracket-location': 'off',
		'no-unused-expressions': 'off',
		'import/order': 'off',
		'import/newline-after-import': 'off',
		'func-names': ['warn', 'as-needed', { generators: 'as-needed' }],
		'max-nested-callbacks': ['error', 6],
		'react/no-deprecated': 'off',
	},
	globals: {
		structuredClone: true,
	},
}
