{"name": "@miriam/oly-render", "version": "1.1.20", "main": "publish/index.js", "dependencies": {"@miriam/super-gif": "0.0.9", "core-js": "^3.30.2", "gif-parser-web": "^1.0.5", "gsap": "^3.11.3", "lossless-json": "^2.0.1", "mathjs": "^11.3.3", "mime": "^3.0.0", "mobx": "^6.9.1", "mobx-react": "^9.0.0", "mobx-state-tree": "^5.1.8", "uuid": "^9.0.0"}, "scripts": {"dev": "sh scripts/dev/dev.sh", "build": "sh scripts/build/build.sh", "build:test": "sh scripts/build/build-test.sh", "build:online": "sh scripts/build/build-online.sh", "package": "sh scripts/pack/pack.sh", "deploy": "npm run package && cd publish && npm publish", "lint": "eslint --ext=jsx,ts,tsx src", "lint-fix": "eslint --ext=jsx,ts,tsx src --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@shark/core": "^1.1.3", "@sharkr/codebox": "^2.0.0", "@sharkr/components": "^2.0.0", "@sharkr/css": "^2.0.0", "@sharkr/eslint-config-react": "^2.0.0", "@sharkr/request": "^1.1.0", "@sharkr/scripts": "^2.0.0", "@types/jest": "24.0.13", "@types/mime": "^3.0.1", "@types/node": "12.0.8", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-router": "5.0.2", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.0", "antd": "^4.21.6", "browserslist": "4.6.2", "clsx": "^1.2.1", "eslint": "^6.8.0", "get-port": "^5.0.0", "koa": "^2.7.0", "koa-body": "^4.1.0", "koa-proxies": "^0.8.1", "koa-webpack-middleware": "^1.0.7", "node-sass": "^4.14.1", "raw-loader": "^4.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-json-view": "^1.21.3", "react-moveable": "^0.44.0", "react-router": "5.1.2", "react-router-dom": "5.1.2", "typescript": "^4.9.5"}}