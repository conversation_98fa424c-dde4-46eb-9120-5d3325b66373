import { getSnapshot } from "mobx-state-tree";
import { LayerModel, isFrame } from "./models";
import { INodeModel, IImageSnapshotIn, ITextSnapshotIn } from "./models/node";
import { PRESET_PRICE } from "./snippet";
import 'core-js/actual/structured-clone';

// 创建价格组，返回snapshot
// export function createPriceGroupSnapshot(color: string, sort?: number) {
//   return getSnapshot(LayerModel.create({ ...PRESET_PRICE, color, sort }))
// }

// // 创建图片
// export function createImageSnapshot(snapShotIn: IImageSnapshotIn) {
//   return getSnapshot(LayerModel.create(snapShotIn))
// }

// // 创建文字
// export function createTextSnapshot(snapShotIn: ITextSnapshotIn) {
//   return getSnapshot(LayerModel.create(snapShotIn))
// }

interface Rect {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 获得两矩形相交的面积
export function getIntersectionArea(rect1: Rect, rect2: Rect) {
  const x = Math.max(rect1.x, rect2.x);
  const y = Math.max(rect1.y, rect2.y);
  const width = Math.min(rect1.x + rect1.width, rect2.x + rect2.width) - x;
  const height = Math.min(rect1.y + rect1.height, rect2.y + rect2.height) - y;
  return width * height;
}

// 判断图片链接是否为nos链接
export const isNosUrl = (url: string) => {
  return /.nosdn.127.net\/.*/.test(url)
}

// nos图片增加后缀参数
export const addNosSuffix = (url: string, suffix: string) => {
  if (!url) return ''
  if (!isNosUrl(url)) return url
  if (url.indexOf('?') > -1) {
    return `${url}&${suffix}`
  } else {
    return `${url}?${suffix}`
  }
}

// 获得当前版本
export const getVersion = () => {
  return '1.1.18'
}

// 解析版本号
export const resolveVersion = (version: string | undefined) => {
  if (!version) return { major: 0, minor: 0, patch: 0 }
  const [major, minor, patch] = version.split('.')
  return {
    major: Number(major),
    minor: Number(minor),
    patch: Number(patch)
  }
}

// 版本比较
export const compareVersion = (schemaVersion: string, renderVersion: string) => {
  const v1 = resolveVersion(schemaVersion)
  const v2 = resolveVersion(renderVersion)
  if (v1.major > v2.major) return 1
  if (v1.major < v2.major) return -1
  if (v1.minor > v2.minor) return 1
  if (v1.minor < v2.minor) return -1
  if (v1.patch > v2.patch) return 1
  if (v1.patch < v2.patch) return -1
  return 0
}

// 版本升级后升级json
export const upgradeSchema = (oldSchema: any) => {
  const version = getVersion()
  const schema = structuredClone(oldSchema)
  // 数据json版本号大于等于当前render版本号，不需要升级
  if (compareVersion(schema.version, version) >= 0) return schema

  // 0.1.8版本升级
  if (compareVersion(schema.version, '0.1.8') < 0) {
    upgradeTo018(schema)
    // upgradeTo100(schema)
  }

  return schema
}

// 0.1.8版本升级
export const upgradeTo018 = (schema: any) => {
  const { trait } = schema
  if (!trait) return
  if (trait.dominantColor && trait.dominantColor.colorList) {

    trait.dominantColor.colorMap = {
      [trait.dominantColor.type]: trait.dominantColor.colorList
    }

    delete trait.dominantColor.colorList
  }
}

// 1.0.0版本升级，去除旧schema中的sort字段，使用数组index排序
// export const upgradeTo100 = (schema: any) => {
//   const { layers } = schema
//   if (!layers?.length) return
//   layers.sort((a: any, b: any) => b.sort - a.sort)
//     .forEach((layer: any) => {
//       delete layer.sort
//     })
// }

// 迭代currentLayer的children, 递归设置children的parent
export const addNodesParent = (currentLayer: INodeModel) => {
  currentLayer.children?.forEach((child) => {
    child.parent = currentLayer
    if (isFrame(child) && child.children?.length) {
      addNodesParent(child)
    }
  })
}

// 获取两个数的最大公约数
export const getGcd = (a: number, b: number): number => {
  if (b === 0) return a
  return getGcd(b, a % b)
}

// TODO: 兼容场景：业务直接调用接口获取模板数据，当模板数据为旧schema时，如果升级，会导致业务重写模板数据接口对应不上的情况
// 需要一个兼容方案，如何保持业务调用的模板数据的持久性，数据结构不会因为render升级而变化