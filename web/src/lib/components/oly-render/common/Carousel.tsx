import React, { useLayoutEffect, useRef } from 'react'
import { observer } from 'mobx-react'
import { ITextModel } from '../models'
import { gsap, Power1 } from 'gsap'
import { FrameText, NodeText } from '../layers'
import { unit } from 'mathjs'

interface IProps {
	model: ITextModel
	paused: boolean
}

export const Carousel: React.FC<IProps> = observer(({ model, paused }) => {
	const carouselRef = useRef<HTMLDivElement>(null)

	useLayoutEffect(() => {
		const ctx = gsap.context(() => {
			const keyframes = model.motion?.contents?.map<GSAPTweenVars>(
				(item, index) => {
					const duration = model.motion?.carouselParams?.stepDuration || 1000
					const delay = model.motion?.carouselParams?.stepDelay || 200
					const durationUnit = unit(duration, 'ms')
					const durationDelay = unit(delay, 'ms')

					return {
						y: -index * model.height,
						duration: durationUnit.toNumber('s'),
						delay: durationDelay.toNumber('s'),
						ease: Power1.easeInOut,
					}
				}
			)

			const tw = gsap.to('.carousel-wrapper', {
				keyframes,
				paused: paused,
				repeat: model.motion?.carouselParams.loop ? -1 : 0,
				onComplete: () => {
					// gsap.to('.carousel-wrapper', { y: 0, duration: 0 })
				},
			})

			if (paused) {
				tw.pause()
			} else {
				tw.play()
			}
		}, carouselRef)
		return () => ctx.revert()
	}, [model.motion?.contents.length, paused])

	return (
		<div
			ref={carouselRef}
			style={{
				width: `${model.width}px`,
				height: `${model.height}px`,
				overflow: 'hidden',
				position: 'relative',
			}}
		>
			<div className='carousel-wrapper'>
				{model.motion?.contents.map((child, index: number) => {
					return (
						<NodeText
							key={index}
							// className={`carousel-item-${index}`}
							model={{ ...model, content: child.text }}
						></NodeText>
					)
				})}
			</div>
		</div>
	)
})
