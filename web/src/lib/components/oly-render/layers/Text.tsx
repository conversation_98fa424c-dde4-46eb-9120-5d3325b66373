import { observer } from 'mobx-react'
import React from 'react'
import { BaseFont, LayerType } from '../const'
import { ITextModel, ITextSnapshotOut } from '../models'
import { Frame } from './Frame'
import { index } from 'mathjs'

const Leaf: React.FC<{
	leaf: ITextSnapshotOut
	model: ITextModel
}> = observer(({ leaf, model }) => {
	return (
		<span
			style={{
				fontSize: `${leaf.fontSize}px`,
				fontFamily: `${leaf.fontFamily}, ${BaseFont}`,
				wordBreak: 'break-all',
				color: `${
					model?.theme?.enable
						? model?.theme?.checkedItem?.color
						: model?.dominantColor?.enable
						? model?.dominantColor?.value
						: leaf.color
				}`,
				textDecoration: leaf.textDecoration
			}}
		>
			{leaf.content}
		</span>
	)
})

export const NodeText: React.FC<{
	model: ITextModel
}> = observer(({ model }) => {
	return (
		<p
			style={{
				display: 'table-cell', // 兼容历史的单行文字数据
				verticalAlign: 'middle',
				width: model.autoLayout?.autoSize ? 'fit-content' : model.width,
				height: model.autoLayout?.autoSize ? 'fit-content' : model.height,
				letterSpacing: `${model.letterSpacing}px`,
				lineHeight: model.displayLineHeight,
				textAlign: model.textAlign as 'left' | 'center' | 'right',
				writingMode: model.writingMode as 'horizontal-tb' | 'vertical-lr',
			}}
		>
			{model.contents?.length ? (
				model.contents.map(leaf => (
					<Leaf model={model} key={leaf.uuid} leaf={leaf}></Leaf>
				))
			) : (
				<Leaf model={model} leaf={model}></Leaf>
			)}
		</p>
	)
})

export const FrameText: React.FC<{
	model: ITextModel
	index: number
}> = observer(({ model, index }) => {
	return (
		<Frame
			model={model}
			index={index}
			// style={{
			// 	width: model.autoSize ? 'fit-content' : model.width,
			// 	height: model.autoSize ? 'fit-content' : model.height,
			// 	borderRadius: model.borderRadiusCSS,
			// 	background: model.background?.enable ? model.background?.css : 'none',
			// 	border: model.border?.enable ? model.border?.css : 0,
			// 	padding: model.paddingCSS,
			// 	overflow: model.clipContent ? 'hidden' : 'visible',
			// }}
		>
			<NodeText model={model}></NodeText>
		</Frame>
	)
})
