import { observer } from 'mobx-react'
import React from 'react'
import { BaseFont, LayerType } from '../const'
import { ITextModel, ITextSnapshotOut } from '../models'
import { Frame } from './Frame'
import { index } from 'mathjs'

const Leaf: React.FC<{
	leaf: ITextSnapshotOut
	model: ITextModel
}> = observer(({ leaf, model }) => {
	// Determine the text color
	const textColor = model?.theme?.enable
		? model?.theme?.checkedItem?.color
		: model?.dominantColor?.enable
		? model?.dominantColor?.value
		: leaf.color

	// Build stroke styles
	const strokeStyles: React.CSSProperties = {}
	if (leaf.stroke?.enable && leaf.stroke.width > 0) {
		// Create text-shadow stroke effect manually
		const shadows = []
		const w = leaf.stroke.width
		const color = leaf.stroke.color
		// Create shadows in 8 directions to simulate stroke
		for (let x = -w; x <= w; x++) {
			for (let y = -w; y <= w; y++) {
				if (x !== 0 || y !== 0) {
					shadows.push(`${x}px ${y}px 0 ${color}`)
				}
			}
		}
		strokeStyles.textShadow = shadows.join(', ')

		// Alternative: use -webkit-text-stroke (but this might affect text fill)
		// strokeStyles.WebkitTextStroke = `${leaf.stroke.width}px ${leaf.stroke.color}`
	}

	return (
		<span
			style={{
				fontSize: `${leaf.fontSize}px`,
				fontFamily: `${leaf.fontFamily}, ${BaseFont}`,
				wordBreak: 'break-all',
				color: textColor,
				textDecoration: leaf.textDecoration,
				...strokeStyles
			}}
		>
			{leaf.content}
		</span>
	)
})

export const NodeText: React.FC<{
	model: ITextModel
}> = observer(({ model }) => {
	return (
		<p
			style={{
				display: 'table-cell', // 兼容历史的单行文字数据
				verticalAlign: 'middle',
				width: model.autoLayout?.autoSize ? 'fit-content' : model.width,
				height: model.autoLayout?.autoSize ? 'fit-content' : model.height,
				letterSpacing: `${model.letterSpacing}px`,
				lineHeight: model.displayLineHeight,
				textAlign: model.textAlign as 'left' | 'center' | 'right',
				writingMode: model.writingMode as 'horizontal-tb' | 'vertical-lr',
			}}
		>
			{model.contents?.length ? (
				model.contents.map(leaf => (
					<Leaf model={model} key={leaf.uuid} leaf={leaf}></Leaf>
				))
			) : (
				<Leaf model={model} leaf={model}></Leaf>
			)}
		</p>
	)
})

export const FrameText: React.FC<{
	model: ITextModel
	index: number
}> = observer(({ model, index }) => {
	return (
		<Frame
			model={model}
			index={index}
			// style={{
			// 	width: model.autoSize ? 'fit-content' : model.width,
			// 	height: model.autoSize ? 'fit-content' : model.height,
			// 	borderRadius: model.borderRadiusCSS,
			// 	background: model.background?.enable ? model.background?.css : 'none',
			// 	border: model.border?.enable ? model.border?.css : 0,
			// 	padding: model.paddingCSS,
			// 	overflow: model.clipContent ? 'hidden' : 'visible',
			// }}
		>
			<NodeText model={model}></NodeText>
		</Frame>
	)
})
