import { observer } from 'mobx-react'
import React from 'react'
import { BaseFont, LayerType } from '../const'
import { ITextModel, ITextSnapshotOut } from '../models'
import { Frame } from './Frame'
import { index } from 'mathjs'

const Leaf: React.FC<{
	leaf: ITextSnapshotOut
	model: ITextModel
}> = observer(({ leaf, model }) => {
	// Determine the text color
	const textColor = model?.theme?.enable
		? model?.theme?.checkedItem?.color
		: model?.dominantColor?.enable
		? model?.dominantColor?.value
		: leaf.color

	// 基础文字样式
	const baseTextStyle: React.CSSProperties = {
		fontSize: `${leaf.fontSize}px`,
		fontFamily: `${leaf.fontFamily}, ${BaseFont}`,
		wordBreak: 'break-all',
		textDecoration: leaf.textDecoration,
	}

	// 如果没有描边，返回普通文本
	if (!leaf.stroke?.enable || leaf.stroke.width <= 0) {
		return (
			<span style={{ ...baseTextStyle, color: textColor }}>
				{leaf.content}
			</span>
		)
	}

	// 有描边时，使用高效的多层text-shadow技术创建向外扩展的描边
	const createOutlineTextShadow = (width: number, color: string) => {
		const shadows = []

		// 使用多层圆形分布的阴影来模拟向外扩展的描边
		// 这种方法比密集采样更高效，同时效果更好
		for (let layer = 1; layer <= width; layer++) {
			const angleStep = layer === 1 ? 45 : 30 // 内层用更少的点，外层用更多的点
			for (let angle = 0; angle < 360; angle += angleStep) {
				const radian = (angle * Math.PI) / 180
				const x = Math.cos(radian) * layer
				const y = Math.sin(radian) * layer
				shadows.push(`${x.toFixed(1)}px ${y.toFixed(1)}px 0 ${color}`)
			}
		}

		return shadows.join(', ')
	}

	return (
		<span
			style={{
				...baseTextStyle,
				color: textColor,
				textShadow: createOutlineTextShadow(leaf.stroke.width, leaf.stroke.color)
			}}
		>
			{leaf.content}
		</span>
	)
})

export const NodeText: React.FC<{
	model: ITextModel
}> = observer(({ model }) => {
	// 计算描边所需的padding
	const getStrokePadding = () => {
		let maxStrokeWidth = 0

		// 检查主文本的描边
		if (model.stroke?.enable && model.stroke.width > 0) {
			maxStrokeWidth = Math.max(maxStrokeWidth, model.stroke.width)
		}

		// 检查contents中每个leaf的描边
		if (model.contents?.length) {
			model.contents.forEach(leaf => {
				if (leaf.stroke?.enable && leaf.stroke.width > 0) {
					maxStrokeWidth = Math.max(maxStrokeWidth, leaf.stroke.width)
				}
			})
		}

		return maxStrokeWidth
	}

	const strokePadding = getStrokePadding()

	return (
		<p
			style={{
				display: 'table-cell', // 兼容历史的单行文字数据
				verticalAlign: 'middle',
				letterSpacing: `${model.letterSpacing}px`,
				lineHeight: model.displayLineHeight,
				textAlign: model.textAlign as 'left' | 'center' | 'right',
				writingMode: model.writingMode as 'horizontal-tb' | 'vertical-lr',
				// 为描边添加padding，但不使用border-box，让容器自然扩展
				padding: strokePadding > 0 ? `${strokePadding}px` : undefined,
			}}
		>
			{model.contents?.length ? (
				model.contents.map(leaf => (
					<Leaf model={model} key={leaf.uuid} leaf={leaf}></Leaf>
				))
			) : (
				<Leaf model={model} leaf={model}></Leaf>
			)}
		</p>
	)
})

export const FrameText: React.FC<{
	model: ITextModel
	index: number
}> = observer(({ model, index }) => {
	return (
		<Frame
			model={model}
			index={index}
      // style={{
			// 	width: model.autoSize ? 'fit-content' : model.width,
			// 	height: model.autoSize ? 'fit-content' : model.height,
			// 	borderRadius: model.borderRadiusCSS,
			// 	background: model.background?.enable ? model.background?.css : 'none',
			// 	border: model.border?.enable ? model.border?.css : 0,
			// 	padding: model.paddingCSS,
			// 	overflow: model.clipContent ? 'hidden' : 'visible',
			// }}
		>
			<NodeText model={model}></NodeText>
		</Frame>
	)
})
