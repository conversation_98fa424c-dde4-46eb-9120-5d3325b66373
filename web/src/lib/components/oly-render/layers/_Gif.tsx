import { observer } from 'mobx-react'
import React, { useLayoutEffect } from 'react'
import { IMediaModel } from '../models'
import { SuperGif } from '@miriam/super-gif'

interface IProps {
	layer: IMediaModel
	paused: boolean
}

export const ElementGif: React.FC<IProps> = observer(({ layer, paused }) => {
	const gifRef = React.useRef<HTMLImageElement>(null)
	const superGif = React.useRef<SuperGif>()

	useLayoutEffect(() => {
		if (!gifRef.current) return
		if (!superGif.current) {
			const c = performance.now()
			superGif.current = new SuperGif(gifRef.current, { autoPlay: false })
			superGif.current.load(() => {
				layer.setDuration(superGif.current?.duration)
				console.log('gif load', performance.now() - c)
			})
			return
		}
		if (superGif.current.isLoading()) return
		if (paused) {
			superGif.current.pause()
			superGif.current.moveTo(0)
		} else {
			superGif.current.play()
		}
		console.log('gif', paused)
	}, [paused])

	return layer.content ? (
		<img
			style={{
				objectFit: layer.fit,
				width: layer.originWidth || layer.width,
				height: layer.originHeight || layer.height,
			}}
			ref={gifRef}
			src={layer.processedContent || layer.content}
		></img>
	) : null
})
