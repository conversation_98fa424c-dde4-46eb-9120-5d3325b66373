import { observer } from 'mobx-react'
import React from 'react'
import { IImageModel } from '../models'
import { Frame } from './Frame'

export const NodeImage: React.FC<{ model: IImageModel }> = observer(
	({ model }) => {
		return model.content ? (
			// <div
			// 	className={`oly-render-node--${LayerType[model.type]}`}
			// 	style={{ width: model.width, height: model.height }}
			// >
			<>
				<img
					style={{
						objectFit: model.fit,
						objectPosition: model.fitPosition,
					}}
					src={
						model.theme?.enable
							? model.theme.checkedItem?.picUrl
							: model.processedContent || model.content
					}
				/>
				{model.mask && (
					<img
						style={{
							objectFit: model.fit,
							objectPosition: model.fitPosition,
						}}
						src={model.mask}
					/>
				)}
			</>
		) : // </div>
		null
	}
)

export const FrameImage: React.FC<{
	model: IImageModel
	index: number
}> = observer(({ model, index }) => {
	return (
		<Frame model={model} index={index}>
			<NodeImage model={model}></NodeImage>
		</Frame>
	)
})
