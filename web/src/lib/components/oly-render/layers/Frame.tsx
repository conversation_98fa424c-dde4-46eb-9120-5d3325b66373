import React from 'react'
import { observer } from 'mobx-react'
import clsx from 'clsx'
import { ILayerModel, isAnchor, isText } from '../models'

interface IProps {
	model: ILayerModel
	index: number
	style?: React.CSSProperties
	className?: string
	id?: string
	children?: React.ReactNode
}

export const Frame: React.FC<IProps> = observer(
	({ children, model, style, className, id, index }) => {
    console.log(model.name, model)
		const getVisibilty = () => {
			if (isText(model) && model.editing) {
				return 'hidden'
			}

			return model.visible ? 'visible' : 'hidden'
		}

		const getDisplay = () => {
			if (isText(model) && !model.contents.length && !model.content) {
				return 'none'
			}

			if (isAnchor(model) && !model.text?.content) {
				return 'none'
			}

			if (model.autoLayout) {
				return 'flex'
			}
			return 'block'
		}

		return (
			<div
				className={clsx('oly-render-frame', className)}
				style={{
					display: getDisplay(),
					position: model.parent?.autoLayout ? 'relative' : 'absolute',
					width: model.autoLayout?.autoSize ? 'fit-content' : model.width,
					height: model.autoLayout?.autoSize ? 'fit-content' : model.height,
					zIndex: index,
					WebkitTransform: model.transformCSS,
					transform: model.transformCSS,
					borderRadius: model.borderRadiusCSS,
					background: model.background?.enable ? model.background?.css : 'none',
					border: model.border?.enable ? model.border?.css : 0,
					padding: model.autoLayout?.paddingCSS,
					gap: model.autoLayout?.gap,
					alignItems: model.autoLayout?.alignItems,
					justifyContent: model.autoLayout?.justifyContent,
					visibility: getVisibilty(),
					overflow: model.clipContent ? 'hidden' : 'visible',
					flexDirection: model.autoLayout?.flexDirection,
					...style,
				}}
				id={id}
			>
				{children}
			</div>
		)
	}
)
