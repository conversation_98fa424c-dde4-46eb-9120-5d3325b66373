import { observer } from 'mobx-react'
import React, { useLayoutEffect } from 'react'
import { IMediaModel } from '../models'
import GifParser from 'gif-parser-web'
import { LayerType } from '../const'
import { Frame } from './Frame'
import { addNosSuffix } from '../helper'

interface IProps {
	model: IMediaModel
	paused: boolean
}

export const NodeGif: React.FC<IProps> = observer(({ model, paused }) => {
	useLayoutEffect(() => {
		if (!model.content) return
		const gifParse = new GifParser(model.content)
		const gifInfo = gifParse.getInfo()
		gifInfo.then(res => {
			const { duration, images } = res
			const d =
				duration > 0 ? duration : images.length > 1 ? images.length * 100 : 0
			model.setDuration(d)
		})
	}, [])
	return model.content ? (
		<div className={`oly-render-node--${LayerType[model.type]}`}>
			<img
				src={addNosSuffix(model.content, 'imageView&extractGif')}
				style={{ display: paused ? 'block' : 'none', objectFit: model.fit }}
			/>
			<img
				src={model.content}
				style={{ display: paused ? 'none' : 'block', objectFit: model.fit }}
			/>
		</div>
	) : null
})

export const NodeVideo: React.FC<IProps> = observer(({ model, paused }) => {
	const videoRef = React.useRef<HTMLVideoElement>(null)
	useLayoutEffect(() => {
		if (videoRef.current) {
			if (paused) {
				videoRef.current.pause()
				videoRef.current.currentTime = 0
			} else {
				videoRef.current.play()
			}
		}
	}, [paused])
	return model.content ? (
		<video ref={videoRef} src={model.content} muted loop playsInline></video>
	) : null
})

export const NodeMedia: React.FC<IProps> = observer(({ model, paused }) => {
	return (
		<div className={`oly-render-node--${LayerType[model.type]}`}>
			{!!model.content && model.mimeType === 'image/gif' && (
				<NodeGif model={model} paused={paused} />
			)}
			{!!model.content && model.mimeType?.startsWith('video/') && (
				<NodeVideo model={model} paused={paused} />
			)}
		</div>
	)
})
