import { observer } from 'mobx-react'
import React from 'react'
import { AnchorDirection, BaseFont } from '../const'
import { IAnchorModel } from '../models'
import { FrameText, NodeText } from './Text'
import { Frame } from './Frame'

export const NodeAnchor: React.FC<{
	model: IAnchorModel
	index: number
}> = observer(({ model, index }) => {
	const { text } = model
	return (
		// <Frame model={model} index={index}>
		<>
			<div
				style={{
					width: 18,
					height: 18,
					border: '5px solid #4b4b4b',
					borderRadius: '50%',
				}}
			></div>
			{text.content && (
				<Frame
					model={text}
					index={index}
					style={{
						position: 'static',
					}}
				>
					<NodeText model={text}></NodeText>
				</Frame>
			)}
		</>
	)
})
