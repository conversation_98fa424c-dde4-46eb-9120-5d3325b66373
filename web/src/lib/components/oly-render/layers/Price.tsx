import { observer, Observer } from 'mobx-react'
import React from 'react'
import { BaseFont, LayerType } from '../const'
import { ITextModel } from '../models'
import { Frame } from './Frame'
import { NodeText } from './Text'

interface IProps {
	model: ITextModel
	index: number
}

// TODO: 使用新的Frame布局替代该组件

export const NodePrice: React.FC<IProps> = observer(({ model, index }) => (
	<div
		// model={model}
		// index={index}
		className={`oly-render-frame--${LayerType[model.type]}`}
	>
		<p
			style={{
				width: model.width,
				height: model.height,
				lineHeight: `${model.height}px`,
				textAlign: model.textAlign as CanvasTextAlign,
			}}
		>
			{model.contents.map((child: any, index: number) => {
				return (
					<span
						key={index}
						style={{
							display: 'inline-block',
							color: child.color,
							lineHeight: 1,
							verticalAlign: child.verticalAlign,
							fontFamily: `${child.fontFamily}, ${BaseFont}`,
							fontSize: `${child.fontSize}px`,
							marginLeft: `${child.marginLeft}px`,
							marginTop: `${child.marginTop}px`,
						}}
					>
						{child.content}
					</span>
				)
			})}
		</p>
		{/* <NodeText model={model}></NodeText> */}
	</div>
))
