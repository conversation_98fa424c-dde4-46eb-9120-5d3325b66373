import { observer } from 'mobx-react'
import React from 'react'
import { ITableModel } from '../models'
import { Frame } from './Frame'

export const NodeTable: React.FC<{
	model: ITableModel
	index: number
}> = observer(({ model, index }) => {
	return (
		<div className='oly-render-node--Table'>
			<table
				style={{
					...model.styleMap.get('table')?.cssStyles,
					width: model.width,
					height: model.height,
					borderRadius: model.borderRadiusCSS,
				}}
			>
				<tbody>
					{model.contents.map((row, rowIndex) => {
						const rowStyles =
							rowIndex === 0
								? model.styleMap.get('thead')?.cssStyles
								: rowIndex % 2 === 0
								? model.styleMap.get('evenRow')?.cssStyles
								: model.styleMap.get('oddRow')?.cssStyles
						return (
							<tr key={rowIndex} style={rowStyles}>
								{row.map((cell, cellIndex) => {
									return cell ? (
										// TODO: colspan/rowspan 暂不支持
										<td
											// colSpan={cell.colspan}
											// rowSpan={cell.rowspan}
											key={cellIndex}
											// style={cell.styleIds.reduce(
											// 	(prev, cur) => {
											// 		return {
											// 			...prev,
											// 			...model.styleMap.get(cur)?.cssStyles,
											// 		}
											// 	},
											// 	{ ...model.styleMap.get('td')?.cssStyles }
											// )}
											style={model.styleMap.get('td')?.cssStyles}
										>
											{cell}
										</td>
									) : null
								})}
							</tr>
						)
					})}
				</tbody>
			</table>
		</div>
	)
})
