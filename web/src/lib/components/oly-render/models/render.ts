import { types } from "mobx-state-tree";

// $nonEmptyObject typescript error：https://github.com/mobxjs/mobx-state-tree/discussions/1699

export const RenderModel = types.model('renderCtrl', {
  motionPaused: types.optional(types.boolean, true),
  duration: types.optional(types.number, 0),
}).actions(self => ({
  setMotionPaused(paused: boolean) {
    self.motionPaused = paused;
  },
  start() {
    self.motionPaused = false
  },
  stop() {
    self.motionPaused = true
  },
  setDuration(time: number) {
    self.duration = time
  }
}));