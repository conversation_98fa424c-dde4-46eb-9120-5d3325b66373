import { types, getSnapshot, applySnapshot, clone, SnapshotIn, Instance, SnapshotOut, IAnyStateTreeNode, cast, detach, resolveIdentifier, destroy } from "mobx-state-tree";
import 'core-js/actual/structured-clone';
import { IDPREFIX, LayerType } from "../const";
import { getIntersectionArea, getVersion, addNodesParent, upgradeSchema } from "../helper";
import { IGoodsModel } from "./node/goods";
import { IImageModel } from "./node/image";
import { ILayerModel, ILayerSnapshotIn, isImage, isText, isGoods, LayerModel, isFrame } from "./layer";
import { LayoutModel } from "./compose/layout";
import { IMediaModel } from "./node/media";
import { ITextModel } from "./node/text";
import { TraitModel } from "./compose/trait";
import { INodeModel } from "./node";

// types.late(() => LayerModel) 用于解决循环引用问题
// IAnyStateTreeNode 避免ts编译TS7056错误 https://github.com/mobxjs/mobx-state-tree/issues/1956
const InteractModel = types.model('Interact', {
  active: types.maybeNull(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel))),
  selected: types.array(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel), { acceptsUndefined: false })),
  hover: types.maybeNull(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel))),
}).volatile(self => ({
  moveableRef: null as any,
})).views(self => ({
  get activeRef() {
    if (self.active) {
      return document.getElementById(`${IDPREFIX}${self.active.uuid}`)
    } else {
      return null
    }
  },
  get selectedRef() {
    return document.querySelectorAll(self.selected.map(layer => `#${IDPREFIX}${layer?.uuid}`).join(','))
  },
  get selectedInOneFrame() {
    return self.selected.every(layer =>
      layer?.parent && layer?.parent === self.selected[0]?.parent
    )
  }
})).actions(self => ({
  setHover(layer: any) {
    self.hover = layer
  },
  setMoveableRef(ref: any) {
    self.moveableRef = ref
  },
  setActive(layer: ILayerModel | null) {
    self.active = layer
  },
  setSelected(layers: any) {
    self.selected = layers
  },
  addSelected(layers: ILayerModel[]) {
    self.selected.push(...layers)
  }
}))

// snapshotProcessor: 预处理数据，用于兼容旧版本数据
export const SchemaModel = types.model('schema', {
  version: types.maybe(types.string),
  layout: types.optional(LayoutModel, {}),
  trait: types.optional(TraitModel, {}),
  layers: types.array(types.late((): IAnyStateTreeNode => LayerModel)),
  keepUp: types.maybe(types.boolean), // 保持最新，schema更新后，模板数据自动升级
  // groupMap: types.map(GroupModel),
  // frames: types.array(types.late((): IAnyStateTreeNode => FrameNodeModel)),
  // layerTree: types.array(types.late((): IAnyStateTreeNode => LayerModel)),
  // frames: types.frozen(), // 用于存储frames数据，不参与业务逻辑
  ext: types.maybe(types.frozen()), // 业务扩展字段, 用于存储业务数据
  interact: types.optional(InteractModel, {}),
}).views(self => ({
  // get layerTree() {
  //   // 合并layers和frames，返回排序后的树形结构
  //   const layerTree = [...self.layers.filter(item => !item.parent)]
  //   self.frames.forEach(frame => layerTree.splice(frame.index, 0, cast(frame)))
  //   return layerTree
  // },
  getNode(id: string) {
    return resolveIdentifier((LayerModel as IAnyStateTreeNode), self.layers, id)
  },
  get inValidLayer() {
    return self.layers.find(item => (isText(item) || isImage(item)) && !item.isValid) as ITextModel | IImageModel | undefined
  },
  get materialIds() {
    return self.layers.map(item => (item as IImageModel).materialId)
  },
  get motionDuration() {
    return Math.max(Math.max(...self.layers.map(item => ((item as ITextModel).motion?.duration || 0))), Math.max(...self.layers.map(item => ((item as IMediaModel).duration || 0))))
  },
  // 获取模板可视区域内尺寸占比最大的商品图层
  get maxGoodsLayer() {
    const goodsLayers = self.layers.filter(item => item.visible && isGoods(item))
    return goodsLayers.reduce((prev, curr) => {
      const layout = getSnapshot(self.layout)
      const prevArea = getIntersectionArea(prev, { ...layout, x: 0, y: 0 })
      const currArea = getIntersectionArea(curr, { ...layout, x: 0, y: 0 })
      return prevArea > currArea ? prev : curr
    }, goodsLayers[0]) as IGoodsModel | undefined
  },
  // get maxSort() {
  //   return Math.max(...self.layers.map(item => item.sort), 0)
  // },
  // 获得layers中，传入图层类型的数量
  getLayerCount(type: LayerType) {
    return self.layers.filter(item => item.type === type).length
  }
})).actions(self => ({
  setExt(ext: object) {
    self.ext = ext
  },
  setLayers(layers: ILayerSnapshotIn[]) {
    applySnapshot(self.layers, layers)
  },
  addLayer(type: number, layer?: any) {
    // const sort = self.maxSort + 1;
    let newLayer = { ...layer }
    const newCount = self.getLayerCount(type) + 1
    switch (type) {
      case LayerType.Text:
        newLayer = { type: 0, content: '请在此编辑', x: 0, y: 0, width: 200, height: 100, name: `${layer?.name || '文字'}${newCount}`, ...layer }
        break;
      case LayerType.Image:
        newLayer = { type, x: 0, y: 0, lockRatio: true, name: `图片${newCount}`, ...layer }
        break;
      case LayerType.Price:
        newLayer = { type, ...layer, name: `${layer?.name || '价格组'}${newCount}`, }
        break;
      case LayerType.Goods:
        newLayer = { type, x: 0, y: 0, lockRatio: true, name: `商品${newCount}`, ...layer }
        break;
      case LayerType.Media:
        newLayer = { type, x: 0, y: 0, lockRatio: true, name: `动图${newCount}`, ...layer }
        break;
      default:
        break;
    }
    self.layers.unshift(newLayer)
    const currentLayer = self.layers[0]
    self.interact.setActive(currentLayer)
    self.interact.setSelected([currentLayer])

    if (isFrame(currentLayer)) {
      addNodesParent(currentLayer)
    }

    return self.layers[0]
  },
  // addFrame(frame: IFrameSnapshotIn) {
  //   self.frames.unshift(frame)
  //   return self.frames[0]
  // },
  copyLayer(layer: ILayerModel) {
    // TODO clone?
    const newLayer: ILayerSnapshotIn = { ...getSnapshot(layer), uuid: undefined, name: `${layer.name}`, x: layer.x + 10, y: layer.y - 10 }
    const index = self.layers.indexOf(layer)
    self.layers.splice(index, 0, newLayer)
    self.interact.setActive(self.layers[index])
    self.interact.setSelected([self.layers[index]])
  },
  remove(layer: any) {
    // const index = self.layers.indexOf(layer)
    // const nextIndex = index === self.layers.length - 1 ? index - 1 : index
    // self.interact.selected.clear()
    // self.layers.remove(self.layers[index])
    // self.interact.setActive(self.layers[nextIndex])
    // self.interact.setSelected([self.layers[nextIndex]])
    destroy(layer)
  },

  resort(from: number, to: number) {

    // ? 当前节点存在子节点，会影响子节点的ref
    // const layer = detach(self.layers[from])

    const layer = getSnapshot(self.layers[from])
    destroy(self.layers[from])
    self.layers.splice(to, 0, layer)
  },

  // 将指定图层移动到最上层
  moveToTop(layers: ILayerModel[]) {
    const newLayers = layers.map(layer => getSnapshot(layer))
    layers.forEach(layer => destroy(layer))
    self.layers.unshift(...newLayers)
  },

  // 将指定图层移动到最下层
  moveToBottom(layers: ILayerModel[]) {
    const newLayers = layers.map(layer => getSnapshot(layer))
    layers.forEach(layer => destroy(layer))
    self.layers.push(...newLayers)
  },

  //  // 重叠的图层
  // getOverlapLayers(x: number, y: number) {
  //   return self.layers.filter(item => item.x <= x && item.y <= y && item.x + item.width >= x && item.y + item.height >= y)
  // }
  // unGroup(group: IGroupModel) {
  //   group.children.forEach(item => {
  //     item.groupId = undefined
  //   })
  //   self.layers.remove(group)
  // },
  // doGroup(layers: ILayerModel[]) {
  //   const groups = Array.from(new Set(layers.filter(item => item.parent).map(item => item.parent)))
  //   groups.forEach(group => {
  //     this.unGroup(self.layers.find(item => item.uuid === group) as IGroupModel)
  //   })
  //   const maxSort = Math.max(...layers.map(item => item.sort))
  //   const minIndex = Math.min(...layers.map(item => self.layers.indexOf(item)))
  //   const newGroup = { sort: maxSort, type: LayerType.Group, name: `组合${self.getLayerCount(LayerType.Group) + 1}`, children: [...layers.map(layer => layer.uuid)] }
  //   self.layers.splice(minIndex, 0, newGroup)
  //   layers.forEach(item => {
  //     item.parent = self.layers[minIndex].uuid
  //     item.sort = maxSort
  //   })
  // }
  unFrame(frame: INodeModel) {
    frame.children?.forEach(item => {
      item.parent = undefined
    })
    // self.frames.remove(frame)
  },
  doFrame(layers: ILayerModel[]) {

  }
})).preProcessSnapshot(sn => {
  const snapshot = upgradeSchema(sn)
  const { layers, frames } = snapshot

  // 按照sort从小到大排序，防止业务数据错乱
  layers?.sort((a: any, b: any) => b.sort - a.sort)

  if (!frames) return snapshot

  const newLayers = layers.filter((item: any) => !item.parent)

  // 迭代遍历所有的frames, 找到对应的layer
  const _makeTree = (frame: any) => {
    frame.children?.forEach((item: any, index: number) => {
      if (item.children) {
        _makeTree(item)
      } else {
        const layer = layers.find((layer: any) => layer.uuid === item)
        frame.children?.splice(index, 1, layer)
      }
    })
    // 过滤掉空的children
    frame.children = frame.children?.filter((item: any) => item)
  }

  frames.forEach((frame: any) => {
    _makeTree(frame)
    const index = frame.index || 0
    newLayers.splice(index, 0, frame)
  })

  snapshot.layers = newLayers

  return snapshot
}).postProcessSnapshot((sn: any) => {
  const snapshot = structuredClone(sn)
  delete snapshot.interact

  const newLayers = snapshot.layers.filter((item: any) => !isFrame(item))

  const frames = snapshot.layers.filter((item: any, index: number) => {
    if (isFrame(item) && item.children) {
      item = { ...item, index }
      // item.index = index
      return true
    } else {
      return false
    }
  })

  // 提取frames内所有的layer
  const _extractLayer = (frame: any, layers: any) => {
    frame.children?.forEach((item: any, index: number) => {
      if (item.children?.length) {
        _extractLayer(item, layers)
      } else {
        frame.children?.splice(index, 1, item.uuid)
        layers.push(item)
      }
    })
  }
  frames.forEach((frame: any) => {
    const layers: any[] = []
    _extractLayer(frame, layers)
    newLayers.splice(frame.index, 0, ...layers)
  })

  // sort倒序，前后出入数据一致
  newLayers.forEach((item: any, index: number) => {
    item.sort = newLayers.length - index
  })

  snapshot.layers = newLayers
  snapshot.frames = frames

  // 标记最新版本号
  snapshot.version = getVersion()
  return snapshot
})

export interface ISchemaModel extends Instance<typeof SchemaModel> { }
export interface ISchemaSnapshotIn extends SnapshotIn<typeof SchemaModel> { }
export interface ISchemaSnapshotOut extends SnapshotOut<typeof SchemaModel> { }