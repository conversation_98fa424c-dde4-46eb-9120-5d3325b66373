import { Instance, types } from "mobx-state-tree";
import { CSSProperties } from "react";
import { LayerType } from "../../const";
import { FrameModel } from "../compose";
import { NodeModel } from ".";

export const CellModel = types.model('tableCell', {
  start: types.array(types.array(types.number)), // [row, col]
  end: types.maybe(types.array(types.array(types.number))), // [row, col]
  styleIds: types.array(types.string),
  colspan: types.maybe(types.number),
  rowspan: types.maybe(types.number),
})

export const TableStyleModel = types.model('tableStyle', {
  id: types.identifier,
  tableLayout: types.maybe(types.enumeration(['auto', 'fixed'])),
  width: types.maybe(types.number),
  minWidth: types.maybe(types.number),
  maxWidth: types.maybe(types.number),
  height: types.maybe(types.number),
  fontSize: types.maybe(types.number),
  fontFamily: types.maybe(types.string),
  fontWeight: types.maybe(types.string),
  fontStyle: types.maybe(types.string),
  textDecoration: types.maybe(types.string),
  textAlign: types.maybe(types.enumeration(['left', 'center', 'right'])),
  color: types.maybe(types.string),
  backgroundColor: types.maybe(types.string),
  lineHeight: types.maybe(types.union(types.number, types.string)),
  padding: types.maybe(types.array(types.number)),
}).views(self => ({
  get cssStyles(): CSSProperties {
    return {
      tableLayout: self.tableLayout as 'auto' | 'fixed',
      width: self.width,
      minWidth: self.minWidth,
      maxWidth: self.maxWidth,
      height: self.height,
      fontSize: self.fontSize,
      fontFamily: self.fontFamily,
      fontWeight: self.fontWeight,
      textDecoration: self.textDecoration,
      textAlign: self.textAlign as 'left' | 'center' | 'right',
      color: self.color,
      backgroundColor: self.backgroundColor,
      lineHeight: self.lineHeight,
      padding: self.padding?.map(p => `${p}px`).join(' '),
    }
  }
}))


export const TableModel = types.compose('table', NodeModel, types.model({
  type: LayerType.Table,
  contents: types.array(types.array(types.string)),
  styleMap: types.map(TableStyleModel),
  cells: types.maybe(types.array(CellModel)),
}))

export interface ITableModel extends Instance<typeof TableModel> { }

