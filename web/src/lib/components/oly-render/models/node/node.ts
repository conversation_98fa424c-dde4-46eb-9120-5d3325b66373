import { types, IAnyStateTreeNode, Instance, SnapshotIn, detach, destroy, getParent, IArrayType, IAnyType, getRoot } from "mobx-state-tree"
import { FrameModel } from "../compose"
import { LayerModel } from "../layer"
import { ISchemaModel } from "../schema"

export const NodeModel = types.compose('FrameNode', FrameModel, types.model({
  // children: types.array(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel))),
  parent: types.maybeNull(types.safeReference(types.late((): IAnyStateTreeNode => NodeModel))),
  children: types.maybe(types.array(types.late((): IAnyStateTreeNode => LayerModel))),
  index: types.optional(types.number, 0),
})).actions(self => ({
  remove() {
    getRoot<ISchemaModel>(self).remove(self)
  },
  setParent(parent: any) {
    self.parent = parent
  },
  setChildren(children: any) {
    self.children = children
  },
  setIndex(index: number) {
    self.index = index
  }
}))

export interface INodeModel extends Instance<typeof NodeModel> { }
export interface INodeSnapshotIn extends SnapshotIn<typeof NodeModel> { }