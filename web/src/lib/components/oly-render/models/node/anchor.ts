import { Instance, types } from "mobx-state-tree";
import { TextModel } from "./text";
import { AnchorDirection, LayerType } from "../../const";
import { NodeModel } from "./node";

export const AnchorModel = types.compose("Anchor", NodeModel, types.model({
  type: LayerType.Anchor,
  text: TextModel,
  direction: types.optional(types.enumeration<AnchorDirection>(Object.values(AnchorDirection)), AnchorDirection.Left),
})).actions(self => ({
  setText(text: string) {
    self.text.setContent(text)
  },
  setDirection(direction: AnchorDirection) {
    self.direction = direction
    self.autoLayout?.setReverse(direction === AnchorDirection.Right)
  },
}))

export interface IAnchorModel extends Instance<typeof AnchorModel> { }