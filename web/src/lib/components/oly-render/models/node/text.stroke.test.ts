import { TextBaseModel, TextModel } from './text'
import { StrokeModel } from '../prop/stroke'

describe('Text Stroke Functionality', () => {
  describe('StrokeModel', () => {
    it('should create stroke model with default values', () => {
      const stroke = StrokeModel.create()
      
      expect(stroke.enable).toBe(false)
      expect(stroke.width).toBe(1)
      expect(stroke.color).toBe('#000000')
    })

    it('should generate correct CSS for text-shadow stroke', () => {
      const stroke = StrokeModel.create({
        enable: true,
        width: 2,
        color: '#ff0000'
      })
      
      const css = stroke.css
      expect(css).toContain('#ff0000')
      expect(css).toContain('2px')
      expect(css).toContain('-2px')
      // Should contain multiple shadow declarations
      expect(css.split(',').length).toBeGreaterThan(1)
    })

    it('should return "none" when stroke is disabled', () => {
      const stroke = StrokeModel.create({
        enable: false,
        width: 2,
        color: '#ff0000'
      })
      
      expect(stroke.css).toBe('none')
    })

    it('should update stroke properties correctly', () => {
      const stroke = StrokeModel.create()
      
      stroke.setEnable(true)
      stroke.setWidth(3)
      stroke.setColor('#00ff00')
      
      expect(stroke.enable).toBe(true)
      expect(stroke.width).toBe(3)
      expect(stroke.color).toBe('#00ff00')
    })
  })

  describe('TextBaseModel with Stroke', () => {
    it('should create text model without stroke by default', () => {
      const text = TextBaseModel.create({
        content: 'Test Text'
      })
      
      expect(text.stroke).toBeUndefined()
    })

    it('should initialize stroke when using initStroke', () => {
      const text = TextBaseModel.create({
        content: 'Test Text'
      })
      
      text.initStroke()
      
      expect(text.stroke).toBeDefined()
      expect(text.stroke?.enable).toBe(false)
    })

    it('should toggle stroke enable/disable', () => {
      const text = TextBaseModel.create({
        content: 'Test Text'
      })
      
      text.toggleStroke(true)
      
      expect(text.stroke).toBeDefined()
      expect(text.stroke?.enable).toBe(true)
      
      text.toggleStroke(false)
      expect(text.stroke?.enable).toBe(false)
    })

    it('should set stroke width and create stroke if not exists', () => {
      const text = TextBaseModel.create({
        content: 'Test Text'
      })
      
      text.setStrokeWidth(5)
      
      expect(text.stroke).toBeDefined()
      expect(text.stroke?.width).toBe(5)
    })

    it('should set stroke color and create stroke if not exists', () => {
      const text = TextBaseModel.create({
        content: 'Test Text'
      })
      
      text.setStrokeColor('#0000ff')
      
      expect(text.stroke).toBeDefined()
      expect(text.stroke?.color).toBe('#0000ff')
    })
  })

  describe('TextModel with Stroke', () => {
    it('should create full text model with stroke functionality', () => {
      const text = TextModel.create({
        content: 'Test Text',
        width: 200,
        height: 50,
        x: 0,
        y: 0
      })
      
      // Initialize stroke
      text.initStroke()
      text.toggleStroke(true)
      text.setStrokeWidth(2)
      text.setStrokeColor('#ff0000')
      
      expect(text.stroke?.enable).toBe(true)
      expect(text.stroke?.width).toBe(2)
      expect(text.stroke?.color).toBe('#ff0000')
      
      // Verify stroke CSS generation
      const css = text.stroke?.css
      expect(css).toContain('#ff0000')
      expect(css).toContain('2px')
    })
  })
})
