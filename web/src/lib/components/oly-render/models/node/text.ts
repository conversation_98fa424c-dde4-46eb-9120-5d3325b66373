import { types, Instance, applySnapshot, SnapshotIn, SnapshotOut } from "mobx-state-tree"
import { ExtModel } from "../prop/ext"
import { MotionModel } from "../prop/motion"
import { v4 as uuidv4 } from "uuid"
import { DominantColorType, LayerType, THEME_KEY } from "../../const"
import { FrameModel } from "../compose"
import { ThemeModel, RefDominantColor, IRefDominantColorSnapshotIn, StrokeModel } from "../prop"
import { NodeModel } from "./node"
import { AigcModel } from '../prop/aigc'

export const TextBaseModel = types.model({
  uuid: types.optional(types.identifier, () => uuidv4()),
  name: types.optional(types.string, "", [null, undefined]),
  content: types.optional(types.string, "", [null, undefined]),
  color: types.optional(types.string, '#000000', [null, undefined]),
  fontSize: types.optional(types.number, 24, [null, undefined]),
  fontFamily: types.optional(types.string, 'FZLanTingHeiS-R-GB', [null, undefined]),
  fontWeight: types.optional(types.string, 'normal', [null, undefined]),
  fontStyle: types.optional(types.string, 'normal', [null, undefined]),
  textDecoration: types.optional(types.string, 'none', [null, undefined]),
  marginLeft: types.optional(types.number, 0, [null, undefined]),
  marginTop: types.optional(types.number, 0, [null, undefined]),
  verticalAlign: types.optional(types.string, "baseline", [null, undefined]),
  stroke: types.maybe(StrokeModel),
  ext: types.maybe(ExtModel),
}).volatile(self => ({
  editing: false,
})).actions(self => ({
  setName(name: string) {
    self.name = name
  },
  setContent(content: string) {
    self.content = content
  },
  setColor(color: string) {
    self.color = color
  },
  setFontSize(fontSize: number) {
    self.fontSize = fontSize
  },
  setFontFamily(fontFamily: string) {
    self.fontFamily = fontFamily
  },
  setFontWeight(fontWeight: string) {
    self.fontWeight = fontWeight
  },
  setFontStyle(fontStyle: string) {
    self.fontStyle = fontStyle
  },
  setTextDecoration(textDecoration: string) {
    self.textDecoration = textDecoration
  },
  setMarginLeft(marginLeft: number) {
    self.marginLeft = marginLeft
  },
  setMarginTop(marginTop: number) {
    self.marginTop = marginTop
  },
  setVerticalAlign(verticalAlign: string) {
    self.verticalAlign = verticalAlign
  },
  setEditing(editing: boolean) {
    self.editing = editing
  },
  initStroke() {
    self.stroke = StrokeModel.create()
  },
  toggleStroke(enable: boolean) {
    if (!self.stroke) {
      self.stroke = StrokeModel.create()
    }
    self.stroke.setEnable(enable)
  },
  setStrokeWidth(width: number) {
    if (!self.stroke) {
      self.stroke = StrokeModel.create()
    }
    self.stroke.setWidth(width)
  },
  setStrokeColor(color: string) {
    if (!self.stroke) {
      self.stroke = StrokeModel.create()
    }
    self.stroke.setColor(color)
  }
}))

export const TextModel = types.compose('Text', TextBaseModel, NodeModel, types.model({
  type: LayerType.Text,
  contents: types.array(TextBaseModel),
  textAlign: types.optional(types.string, "left", [null, undefined]),
  lineHeight: types.optional(types.number, 1.5, [null, undefined]),
  letterSpacing: types.optional(types.number, 0, [null, undefined]),
  writingMode: types.optional(types.string, "horizontal-tb", [null, undefined]),
  dominantColor: types.maybe(RefDominantColor),
  motion: types.maybe(MotionModel),
  theme: types.maybe(ThemeModel),
  aigc: types.maybe(AigcModel)
})).views(self => ({
  get isValid() {
    if (self.ext?.required === false) return true
    return !!self.content
  },
  get maxFontSize() {
    const fzs = self.contents.map((item: any) => item.fontSize)
    return Math.max(...fzs, self.fontSize)
  },
  get displayLineHeight() {
    const dplh = self.autoLayout?.autoSize || self.height > self.lineHeight * self.fontSize
      ? self.lineHeight
      : `${self.height}px`
    return dplh
  }
})).actions((self) => ({
  setContents(contents: any) {
    applySnapshot(self.contents, contents)
  },
  setTextAlign(textAlign: string) {
    self.textAlign = textAlign
  },
  setLineHeight(lineHeight: number) {
    self.lineHeight = lineHeight
  },
  setLetterSpacing(letterSpacing: number) {
    self.letterSpacing = letterSpacing
  },
  setWritingMode(writingMode: string) {
    self.writingMode = writingMode
  },
  initMotion() {
    self.motion = MotionModel.create({ contents: [{ text: self.content }] })
  },
  toggleDominantColor(enable: boolean, data?: IRefDominantColorSnapshotIn) {
    if (!self.dominantColor) {
      self.dominantColor = RefDominantColor.create({ type: DominantColorType.Common, ...data })
    }
    self.dominantColor.setEnable(enable)
  },
  initTheme() {
    self.theme = ThemeModel.create({ list: [{ label: 'dark', color: '#000', key: THEME_KEY.newKey }, { label: 'light', color: '#fff', key: THEME_KEY.newKey }] })
    self.theme.setChecked(THEME_KEY.key)
  },
  initAigc(enable?: boolean) {
    self.aigc = AigcModel.create({
      enable: !!enable
    })
  }
}))

export interface ITextModel extends Instance<typeof TextModel> { }
export interface ITextSnapshotIn extends SnapshotIn<typeof TextModel> { }
export interface ITextBaseSnapshotIn extends SnapshotIn<typeof TextBaseModel> { }
export interface ITextSnapshotOut extends SnapshotOut<typeof TextBaseModel> { }