import { applyPatch, applySnapshot, Instance, SnapshotIn, types } from "mobx-state-tree"
import { ImageFitType, THEME_KEY } from "../../const"
import { MotionModel } from "../prop/motion"
import { IProcessingSnapshotIn, ProcessingModel } from "../prop/process"
import { FillModel } from "../prop/fill"
import { FrameModel } from "../compose"
import { ThemeModel } from "../prop/theme"
import { NodeModel } from "."

export const ImageModel = types.compose('Image', NodeModel, types.model({
  content: types.optional(types.string, ''),
  mask: types.optional(types.string, ''),
  materialId: types.optional(types.number, 0),
  originWidth: types.optional(types.number, 0),
  originHeight: types.optional(types.number, 0),
  fit: types.optional(types.enumeration<ImageFitType>(Object.values(ImageFitType)), ImageFitType.Fill),
  fitPosition: types.optional(types.string, '50% 50%'),
  fixedSize: types.optional(types.boolean, false),
  motion: types.maybe(MotionModel),
  processing: types.array(ProcessingModel),
  fill: types.maybe(FillModel),
  theme: types.maybe(ThemeModel),
  prompt: types.maybeNull(types.string),
  tags: types.maybeNull(types.frozen<string[]>()),
  subPic: types.maybeNull(types.frozen<{ softedge: string }>())
}))
  .views(self => ({
    get isValid() {
      if (self.ext?.required === false) return true
      return !!self.content
    },
    get processedContent() {
      if (!self.processing.length) return null;
      return self.processing[self.processing.length - 1].result
    },
    get currentProcessing() {
      if (!self.processing.length) return null;
      return self.processing[self.processing.length - 1]
    }
  }))
  .actions(self => ({
    setMask(mask: string) {
      self.mask = mask
    },
    setFixedSize(fixedSize: boolean) {
      self.fixedSize = fixedSize
    },
    setFit(fit: ImageFitType) {
      self.fit = fit
    },
    setFitPosition(fitPosition: string) {
      self.fitPosition = fitPosition
    },
    addProcessing(processing: IProcessingSnapshotIn) {
      if (!self.processing) {
        applySnapshot(self.processing, [processing])
        console.log(self.processing)
      } else {
        self.processing.push(processing)
      }
    },
    removeProcessing() {
      self.processing.pop()
    },
    clearProcessing() {
      self.processing.clear()
    },
    patch(data: any) {
      Object.keys(data).forEach(key => {
        applyPatch(self, { op: 'replace', path: `/${key}`, value: data[key] })
      })
    },
    setContent(c: string, width?: number, height?: number) {
      self.content = c
      if (self.fixedSize) return
      self.width = width || self.width
      self.height = height || self.height
      self.originWidth = width || self.originWidth
      self.originHeight = height || self.originHeight
    },
    initFill(colorMap: any) {
      self.fill = FillModel.create({
        enable: true,
        colorMap,
        degree: 0,
      })
    },
    initTheme() {
      self.theme = ThemeModel.create({ list: [{ label: 'dark', color: '#000', key: THEME_KEY.newKey }, { label: 'light', color: '#fff', key: THEME_KEY.newKey }] })
      self.theme.setChecked(THEME_KEY.key)
    },
  }))

export interface IImageModel extends Instance<typeof ImageModel> { }
export interface IImageSnapshotIn extends SnapshotIn<typeof ImageModel> { }