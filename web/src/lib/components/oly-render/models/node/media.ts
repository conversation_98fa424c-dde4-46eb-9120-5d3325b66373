import { types, Instance } from "mobx-state-tree";
import mime from 'mime/lite'
import { LayerType } from "../../const";
import { ImageModel } from "./image";

export const MediaModel = types.compose('Media', ImageModel, types.model({
  type: LayerType.Media,
  content: types.optional(types.string, ''),
  duration: types.optional(types.number, 0),
  delay: types.optional(types.number, 0),
})).views(self => ({
  get mimeType() {
    return self.content ? mime.getType(self.content) : undefined
  }
})).actions(self => ({
  setDuration(duration: number) {
    self.duration = duration
  }
}))

export interface IMediaModel extends Instance<typeof MediaModel> { }