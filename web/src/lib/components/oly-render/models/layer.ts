import { Instance, SnapshotIn, types } from "mobx-state-tree"
import { LayerType } from "../const"
import { ITextModel, TextModel, INodeModel, NodeModel, IAnchorModel, AnchorModel, ITableModel, TableModel, IImageModel, ImageModel, IMediaModel, MediaModel, GoodsModel, IGoodsModel } from "./node"

export const LayerModel = types.union({
  dispatcher: (snapshot) => {
    switch (snapshot.type) {
      case LayerType.Text:
      case LayerType.Price:
        return TextModel
      case LayerType.Goods:
        return GoodsModel
      case LayerType.Image:
        return ImageModel
      case LayerType.Media:
        return MediaModel
      case LayerType.Table:
        return TableModel
      case LayerType.Anchor:
        return AnchorModel
      case LayerType.Frame:
        return NodeModel
      default:
        return types.undefined
    }
  }
}, TextModel, ImageModel, GoodsModel, MediaModel, TableModel, AnchorModel, NodeModel)

// export const LayerModel = types.compose('layer', TextModel, ImageModel, GoodsModel, MediaModel, TableModel, GroupModel, AnchorModel)

export const isText = (layer?: ILayerModel | null): layer is ITextModel => {
  return layer?.type === LayerType.Text
}

export const isPrice = (layer?: ILayerModel | null): layer is ITextModel => {
  return layer?.type === LayerType.Price
}

export const isImage = (layer?: ILayerModel | null): layer is IImageModel => {
  return layer?.type === LayerType.Image
}

export const isGoods = (layer?: ILayerModel | null): layer is IGoodsModel => {
  return layer?.type === LayerType.Goods
}

export const isMedia = (layer?: ILayerModel | null): layer is IMediaModel => {
  return layer?.type === LayerType.Media
}

export const isTable = (layer?: ILayerModel | null): layer is ITableModel => {
  return layer?.type === LayerType.Table
}

export const isAnchor = (layer?: ILayerModel | null): layer is IAnchorModel => {
  return layer?.type === LayerType.Anchor
}

export const isFrame = (layer?: ILayerModel | null): layer is INodeModel => {
  return layer?.type === LayerType.Frame
}

export type ILayerModel = Instance<typeof LayerModel>
export type ILayerSnapshotIn = SnapshotIn<typeof LayerModel>