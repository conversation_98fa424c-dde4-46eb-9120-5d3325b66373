import { types, Instance, SnapshotIn } from "mobx-state-tree";
import { DominantColorType } from "../../const";

export const RefDominantColor = types.model('RefDominantColor', {
  type: types.enumeration<DominantColorType>(Object.values(DominantColorType)),
  value: types.maybe(types.string),
  enable: types.optional(types.boolean, false)
}).actions(self => ({
  setType(type: DominantColorType) {
    self.type = type
  },
  setValue(value: string) {
    self.value = value
  },
  setEnable(enable: boolean) {
    self.enable = enable
  }
}));

export interface IRefDominantColor extends Instance<typeof RefDominantColor> { }
export interface IRefDominantColorSnapshotIn extends SnapshotIn<typeof RefDominantColor> { }

export const DominantColor = types.model('DominantColor', {
  n: types.optional(types.number, 10),
  colorMap: types.map(types.frozen<string[]>()),
}).actions(self => ({
  setN(n: number) {
    self.n = n
  },
  setColorMap(type: DominantColorType, colorMap: string[]) {
    self.colorMap.set(type, colorMap)
  }
}));

export interface IDominantColor extends Instance<typeof DominantColor> { }
export interface IDominantColorSnapshotIn extends SnapshotIn<typeof DominantColor> { }