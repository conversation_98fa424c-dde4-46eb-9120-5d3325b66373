/*
 * @Description: 
 * @Author: hzweixin <EMAIL>
 * @Date: 2023-04-08 19:21:40
 * @LastEditors: hzweixin <EMAIL>
 * @LastEditTime: 2023-06-06 11:12:01
 */
import { types } from "mobx-state-tree"
import { ColorType } from "../../const";

export const FillModel = types.model('Fill', {
  enable: types.optional(types.boolean, false),
  degree: types.optional(types.number, 0), // pcp: degree>0取深色度, type=ColorType.Dark; degree<0取浅色度, type=ColorType.Light
  n: types.optional(types.number, 10),
  colorMap: types.map(types.frozen<string[]>()),  // 颜色列表
  type: types.optional(types.enumeration<ColorType>(Object.values(ColorType)), ColorType.Dark),
}).views(self => ({
  get color() {
    const colorList = self.colorMap.get(self.type) || []
    return colorList[self.degree]
  }
})).actions(self => ({
  setEnable(enable: boolean) {
    self.enable = enable
  },
  pickColor(type: ColorType, degree: number) {
    self.type = type
    self.degree = degree
  },
  setColorMap(type: ColorType, colorMap: string[]) {
    self.colorMap.set(type, colorMap)
  }
}));
