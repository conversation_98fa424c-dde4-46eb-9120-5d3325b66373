import { types } from "mobx-state-tree";
import { AutoLayoutDirection } from "../../const";
import { CSSProperties } from "react";

export const AutoLayoutModel = types.model('AutoLayout', {
  direction: types.optional(types.enumeration<AutoLayoutDirection>(Object.values(AutoLayoutDirection)), AutoLayoutDirection.Row),
  reverse: types.optional(types.boolean, false),
  gap: types.optional(types.number, 0),
  justifyContent: types.optional(types.string, 'flex-start'),
  alignItems: types.optional(types.string, 'flex-start'),
  padding: types.optional(types.union(types.number, types.frozen([0, 0, 0, 0])), 0),
  autoWidth: types.optional(types.boolean, true),
  autoHeight: types.optional(types.boolean, true),
}).views(self => ({
  get paddingCSS() {
    if (typeof self.padding === 'number') {
      return `${self.padding}px`
    } else if (Array.isArray(self.padding)) {
      return self.padding.map(v => `${v}px`).join(' ')
    } else {
      return 0
    }
  },
  get autoSize() {
    return self.autoWidth && self.autoHeight
  },
  get flexDirection(): CSSProperties['flexDirection'] {
    const reverse = self.reverse ? '-reverse' : ''
    switch (self.direction) {
      case AutoLayoutDirection.Row:
        return `row${reverse}`
      case AutoLayoutDirection.Column:
        return `column${reverse}`
      default:
        return `row${reverse}`
    }
  }
})).actions(self => ({
  setReverse(reverse: boolean) {
    self.reverse = reverse
  },
  setGap(gap: number) {
    self.gap = gap
  },
  setJustifyContent(justifyContent: string) {
    self.justifyContent = justifyContent
  },
  setDirection(direction: AutoLayoutDirection) {
    self.direction = direction
  },
  setAlignItems(alignItems: string) {
    self.alignItems = alignItems
  },
  setAutoSize(autoSize: boolean) {
    self.autoWidth = autoSize
    self.autoHeight = autoSize
  }
}))
