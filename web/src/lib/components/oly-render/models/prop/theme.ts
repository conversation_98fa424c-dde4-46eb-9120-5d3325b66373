/*
 * @Description: 
 * @Author: hzweixin <EMAIL>
 * @Date: 2023-04-06 19:34:31
 * @LastEditors: hzweixin <EMAIL>
 * @LastEditTime: 2023-06-06 17:44:36
 */
import { Instance, SnapshotOrInstance, castToReferenceSnapshot, destroy, types } from "mobx-state-tree";
import { THEME_KEY } from "../../const";

export const ThemeItemModel = types.model("ThemeItem", {
  key: types.number, // 主题id
  color: types.maybe(types.string),
  label: types.maybe(types.string), // 主题名称 dark, light
  picUrl: types.maybe(types.string),
}).actions(self => ({
  setColor(color: string) {
    self.color = color;
  },
  setLabel(label: string) {
    self.label = label;
  },
  setPicUrl(picUrl: string) {
    self.picUrl = picUrl;
  },
}));

export const ThemeModel = types.model("Theme", {
  list: types.array(ThemeItemModel),
  checked: types.maybeNull(types.number) ,
  enable: types.optional(types.boolean, false)
}).views(self => ({
  get checkedItem() {
    return self.list.find(item => item.key === self.checked)
  }
})).actions(self => ({
  setList(list: any) {
    self.list = list;
  },
  setChecked(theme: any) {
    self.checked = theme
  },
  delete(theme: any) {
    const index = self.list.indexOf(theme)
    const nextIndex = index === self.list.length - 1 ? index - 1 : index;
    self.list.splice(index, 1)
    if (!self.list.length) {
      self.checked = null
      self.enable = false
    } else {
      self.checked = self.list[nextIndex].key
    }
  },
  add(item: { color: string; label: string; picUrl: string; }) {
    const maxKey = self.list.reduce((max, item) => Math.max(max, item.key), 0);
    const newKey = maxKey + 1
    self.list.push({ ...item, label: `${item.label}${newKey}`, key: newKey });
  },
  setEnable(enable: boolean) {
    self.enable = enable;
  }
}));

export interface IThemeModel extends Instance<typeof ThemeModel> { }
