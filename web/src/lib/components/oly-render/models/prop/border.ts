import { types } from "mobx-state-tree";

export const BorderModel = types.model('border', {
  enable: types.optional(types.boolean, true),
  width: types.optional(types.number, 1),
  color: types.optional(types.string, '#000000'),
  style: types.optional(types.string, 'solid'),
}).views(self => ({
  get css() {
    return `${self.width}px ${self.style} ${self.color}`
  }
})).actions(self => ({
  setWidth(width: number) {
    self.width = width
  },
  setColor(color: string) {
    self.color = color
  },
  setStyle(style: string) {
    self.style = style
  },
}))