import { SnapshotIn, types } from "mobx-state-tree";
import { ProcessingOp } from "../../const";

export interface ProcessingParams {
  aspect_ratio?: number, // 裁剪后的长宽比，默认是1
  shadow_color?: string, // 阴影颜色，默认是 #3030303C
}

export const ProcessingModel = types.model('processing', {
  op: types.enumeration<ProcessingOp>(Object.values(ProcessingOp)),
  params: types.maybe(types.frozen<ProcessingParams>()),
  result: types.maybe(types.string),
}).actions(self => ({
  setResult(result: string) {
    self.result = result;
  }
}));

export interface IProcessingSnapshotIn extends SnapshotIn<typeof ProcessingModel> { }