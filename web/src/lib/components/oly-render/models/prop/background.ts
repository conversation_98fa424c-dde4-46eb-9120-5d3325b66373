import { types } from "mobx-state-tree";

export const BackgroundModel = types.model('Background', {
  enable: types.optional(types.boolean, true),
  color: types.optional(types.string, '#ffffff'),
  image: types.optional(types.string, ''),
}).views(self => ({
  get css() {
    const b = `${self.image ? `url(${self.image})` : ''}${self.color ? ` ${self.color}` : ``}`
    return b || 'none'
  }
})).actions(self => ({
  setColor(color: string) {
    self.color = color
  },
  setImage(image: string) {
    self.image = image
  },
  setEnable(enable: boolean) {
    self.enable = enable
  }
}))