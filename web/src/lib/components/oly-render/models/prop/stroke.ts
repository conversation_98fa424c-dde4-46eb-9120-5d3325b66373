import { types } from "mobx-state-tree";

export const StrokeModel = types.model('Stroke', {
  enable: types.optional(types.boolean, false),
  width: types.optional(types.number, 1),
  color: types.optional(types.string, '#000000'),
}).views(self => ({
  get css() {
    if (!self.enable) return 'none'
    // Using text-shadow to create stroke effect with multiple shadows
    const shadows = []
    const w = self.width
    // Create shadows in 8 directions to simulate stroke
    for (let x = -w; x <= w; x++) {
      for (let y = -w; y <= w; y++) {
        if (x !== 0 || y !== 0) {
          shadows.push(`${x}px ${y}px 0 ${self.color}`)
        }
      }
    }
    return shadows.join(', ')
  },
  get webkitStroke() {
    if (!self.enable) return {}
    return {
      WebkitTextStroke: `${self.width}px ${self.color}`,
      WebkitTextFillColor: 'transparent'
    }
  }
})).actions(self => ({
  setEnable(enable: boolean) {
    self.enable = enable
  },
  setWidth(width: number) {
    self.width = width
  },
  setColor(color: string) {
    self.color = color
  },
}))
