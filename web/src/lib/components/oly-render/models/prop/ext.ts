/*
 * @Description: 
 * @Author: h<PERSON>wei<PERSON> <EMAIL>
 * @Date: 2023-03-29 15:25:46
 * @LastEditors: hzweixin <EMAIL>
 * @LastEditTime: 2023-05-30 13:54:16
 */
import { types } from "mobx-state-tree"

export const ExtModel = types.model('Ext', {
  required: types.optional(types.boolean, true), // 是否必填
  maxLength: types.maybe(types.number), // 文字最大长度
  edit: types.optional(types.boolean, true), // 是否可编辑替换
  replaceGoodsImage: types.maybe(types.number), // 是否需要替换商品图 1-itemId 2-skuId 
  goodsImageType: types.maybe(types.number), // 商品图类型 1-场景图 2-透底图
}).actions(self => ({
  setMaxLength(maxLength: number) {
    self.maxLength = maxLength
  },
  setEdit(edit: boolean) {
    self.edit = edit
  },
  setReplaceGoodsImage(replaceGoodsImage: number) {
    self.replaceGoodsImage = replaceGoodsImage
  },
  setRequired(required: boolean) {
    self.required = required
  },
  setGoodsImageType(goodsImageType: number) {
    self.goodsImageType = goodsImageType
  }
}))