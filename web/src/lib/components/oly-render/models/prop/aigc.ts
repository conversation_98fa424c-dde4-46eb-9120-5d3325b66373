import { types } from 'mobx-state-tree'
import { AigcType } from '../../const'

export const AigcModel = types
	.model('AigcModel', {
		enable: types.optional(types.boolean, false),
		n: types.optional(types.number, 5), // 指定生成结果的数量
    reference: types.optional(types.string, ''), // 参考内容
    list: types.optional(types.array(types.string), []), // 生成结果
		type: types.optional(
			types.enumeration<AigcType>(Object.values(AigcType)),
      AigcType.智能
		),
	})
	.actions(self => ({
		setEnable(enable: boolean) {
			self.enable = enable
		},
    setReference(reference: string) {
      self.reference = reference
    },
    setList(list: any) {
      self.list = list
    },
    setType(type: any) {
      self.type = type
    }
	}))
