import { clone, types } from "mobx-state-tree";
import { v4 as uuid } from "uuid";
import { MotionType } from "../../const";

const MotionParams = types.model("MotionParams", {
  loop: types.optional(types.boolean, false),
  stepDuration: types.optional(types.number, 300),
  stepDelay: types.optional(types.number, 1000),
  carouselName: types.optional(types.string, ""),
})

const CarouselContent = types.model("CarouselContent", {
  width: types.maybe(types.number),
  height: types.maybe(types.number),
  text: types.optional(types.string, ""),
}).actions(self => ({
  setText(text: string) {
    self.text = text
  },
}))

const MotionItem = types.model("MotionItem", {
  uuid: types.optional(types.string, () => uuid()),
  ease: types.maybe(types.string),
  duration: types.maybe(types.number),
  delay: types.maybe(types.number)
})

export const MotionModel = types.model('motion', {
  // type: types.enumeration("type", ['none', 'normal', 'carousel']),
  type: types.optional(types.enumeration<MotionType>(Object.values(MotionType)), MotionType.None),
  motions: types.array(MotionItem),
  carouselParams: types.optional(MotionParams, { loop: false, stepDuration: 300, stepDelay: 1000, carouselName: "" }),
  contents: types.array(CarouselContent),
  delay: types.maybe(types.number)
})
  .views(self => ({
    get duration() {
      if (self.type === MotionType.Carousel) {
        if (self.contents.length > 1) {
          return (self.carouselParams.stepDuration + self.carouselParams.stepDelay) * self.contents.length
        } else {
          return 0
        }
      } else {
        return self.motions.reduce((acc, cur) => acc + (cur.duration || 0), 0)
      }
    }
  }))
  .actions(self => ({
    addCarousel(content: any) {
      self.contents.push(content)
    },
    removeCarousel(index: number) {
      self.contents.splice(index, 1)
    },
    setType(type: MotionType) {
      self.type = type
    }
  }))