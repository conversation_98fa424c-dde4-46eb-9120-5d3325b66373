/*
 * @Description: Trait Model
 * @Author: hzweixin <EMAIL>
 * @Date: 2023-03-29 15:36:38
 * @LastEditors: hzweixin <EMAIL>
 * @LastEditTime: 2023-11-13 11:12:58
 */
import { types, Instance, cast, IAnyStateTreeNode } from "mobx-state-tree";
import { DominantColor, IDominantColorSnapshotIn } from "../prop/dominantColor";
import { LayerModel } from "../layer";

export const TraitModel = types.model('Trait', {
  mainItemId: types.optional(types.string, ''),
  mainColor: types.optional(types.string, ''),
  motion: types.optional(types.boolean, false),
  duration: types.optional(types.number, 0, [null, undefined]),
  dominantColor: types.maybe(DominantColor), // 算法特征颜色
  dominantNode: types.maybe(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel))), // 算法特征节点
  mainGoodsNode: types.maybe(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel))), // 主商品节点
  aigcNode: types.array(types.safeReference(types.late((): IAnyStateTreeNode => LayerModel), { acceptsUndefined: false })) // aigc图片替换节点
}).actions(self => ({
  setMainItemId(id: string) {
    self.mainItemId = id
  },
  setMainColor(color: string) {
    self.mainColor = color
  },
  setMotion(motion: boolean) {
    self.motion = motion
  },
  setDuration(duration: number) {
    self.duration = duration
  },
  setDominantColor(data: IDominantColorSnapshotIn) {
    self.dominantColor = cast(data)
  },
  setDominantNode(node: any) {
    self.dominantNode = node
  },
  setMainGoodsNode(node: any) {
    self.mainGoodsNode = node
  },
  setAigcNode(node: any) {
    if (self.aigcNode.includes(node)) return
    self.aigcNode.push(node)
  },
  unsetAigcNode(node: any) {
    const index = self.aigcNode.findIndex(v => v.uuid === node.uuid)
    self.aigcNode.splice(index, 1)
  }
}));

export interface ITraitModel extends Instance<typeof TraitModel> { }
