import { types } from "mobx-state-tree"
import { v4 as uuidv4 } from "uuid"
import { LayerTypeName } from "../../const"
import { ExtModel } from "../prop"
import { isNumber } from "mathjs"

export const BasePropModel = types.model('BaseProp', {
  uuid: types.optional(types.identifier, () => uuidv4()),
  type: types.number,
  name: types.optional(types.string, ""),
  x: types.optional(types.number, 0),
  y: types.optional(types.number, 0),
  width: types.optional(types.number, 0),
  height: types.optional(types.number, 0),
  lockRatio: types.optional(types.boolean, false),
  angle: types.optional(types.number, 0),
  opacity: types.optional(types.number, 1),
  // sort: types.optional(types.number, 0),
  locked: types.optional(types.boolean, false),
  visible: types.optional(types.boolean, true),
  ext: types.maybe(ExtModel),
}).views(self => ({
  get typeName() {
    return LayerTypeName[self.type]
  },
  get transformCSS() {
    return `translate(${self.x}px, ${self.y}px)${self.angle ? ` rotate(${self.angle}deg)` : ''} `
  },
})).actions(self => ({
  // setSort(sort: number) {
  //   self.sort = sort
  // },
  setLockRatio(lockRatio: boolean) {
    self.lockRatio = lockRatio
  },
  setSize(width: number, height: number) {
    self.width = width
    self.height = height
  },
  setWidth(width: number) {
    if (self.lockRatio) {
      self.height = width / self.width * self.height
    }
    self.width = width
  },
  setHeight(height: number) {
    if (self.lockRatio) {
      self.width = height / self.height * self.width
    }
    self.height = height
  },
  setName(name: string) {
    self.name = name
  },
  setX(x: number) {
    self.x = x
  },
  setY(y: number) {
    self.y = y
  },
  setPosition(x: number, y: number) {
    self.x = x
    self.y = y
  },
  setAngle(angle: number) {
    self.angle = angle
  },
  setLocked(locked: boolean) {
    self.locked = locked
  },
  setVisible(visible: boolean) {
    self.visible = visible
  },
}))