import { types, Instance, castToSnapshot } from "mobx-state-tree";
import { DominantColorType } from "../../const";
import { RefDominantColor, IRefDominantColorSnapshotIn } from "../prop";
import { getGcd } from "../../helper";

export const LayoutModel = types.model('Layout', {
  width: types.optional(types.number, 800),
  height: types.optional(types.number, 800),
  lockAspectRatio: types.optional(types.boolean, false),
  backgroundColor: types.maybe(types.string),
  backgroundImage: types.maybe(types.string),
  dominantColor: types.maybe(RefDominantColor)
}).views(self => ({
  get background() {
    const b = `${self.backgroundImage ? `url(${self.backgroundImage})` : ''}${self.backgroundColor ? ` ${self.backgroundColor}` : ``}`
    return b || 'none'
  },
  get aspectRatio() {
    const gcd = getGcd(self.width, self.height)
    return [Math.round(self.width / gcd), Math.round(self.height / gcd)]
  },
  get aspectRatioText() {
    const gcd = getGcd(self.width, self.height)
    return `${Math.round(self.width / gcd)}:${Math.round(self.height / gcd)}`
  }
})).actions(self => ({
  resize({ width, height }: { width?: number, height?: number }) {
    self.width = width || self.width
    self.height = height || self.height
  },
  setWidth(width: number) {
    if (self.lockAspectRatio) {
      self.height = Math.round(width * self.height / self.width)
    }
    self.width = Math.round(width)
  },
  setHeight(height: number) {
    if (self.lockAspectRatio) {
      self.width = Math.round(height * self.width / self.height)
    }
    self.height = Math.round(height)
  },
  setBackgroundColor(color: string) {
    self.backgroundColor = color
  },
  setBackgroundImage(url: string) {
    self.backgroundImage = url
  },
  toggleDominantColor(enable: boolean, data?: IRefDominantColorSnapshotIn) {
    if (!self.dominantColor) {
      self.dominantColor = RefDominantColor.create({ type: DominantColorType.Common, ...data })
    }
    self.dominantColor.setEnable(enable)
  }
}));

export interface ILayoutModel extends Instance<typeof LayoutModel> { }