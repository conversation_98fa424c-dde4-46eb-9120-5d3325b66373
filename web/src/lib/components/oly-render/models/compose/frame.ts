import { types } from "mobx-state-tree";
import { BorderModel, BackgroundModel, AutoLayoutModel, ExtModel } from "../prop";
import { v4 as uuidv4 } from "uuid"
import { LayerTypeName, IDPREFIX } from "../../const"


// frame作为千画layer的基本构成单元，包含了基本的属性，以及一些基本的操作。每个千画layer都被一个frame包裹。
export const FrameModel = types.model('FrameProps', {
  uuid: types.optional(types.identifier, () => uuidv4()),
  type: types.number,
  name: types.optional(types.string, ""),
  x: types.optional(types.number, 0),
  y: types.optional(types.number, 0),
  width: types.optional(types.number, 0),
  height: types.optional(types.number, 0),
  lockRatio: types.optional(types.boolean, false),
  angle: types.optional(types.number, 0),
  opacity: types.optional(types.number, 1),
  // sort: types.optional(types.number, 0),
  locked: types.optional(types.boolean, false),
  visible: types.optional(types.boolean, true),
  ext: types.optional(ExtModel, {}),
  borderRadius: types.optional(types.union(types.number, types.frozen([0, 0, 0, 0])), 0),
  clipContent: types.optional(types.boolean, true),
  border: types.maybe(BorderModel),
  background: types.maybe(BackgroundModel),
  autoLayout: types.maybe(AutoLayoutModel),
}).volatile(self => ({
  isProcessing: false as boolean,
})).views(self => ({
  get borderRadiusCSS() {
    if (typeof self.borderRadius === 'number') {
      return `${self.borderRadius}px`
    } else if (Array.isArray(self.borderRadius)) {
      return self.borderRadius.map(v => `${v}px`).join(' ')
    } else {
      return 0
    }
  },
  get typeName() {
    return LayerTypeName[self.type]
  },
  get transformCSS() {
    return `translate(${self.x}px, ${self.y}px)${self.angle ? ` rotate(${self.angle}deg)` : ''} `
  },
  get widthCSS() {
    if (self.autoLayout?.autoWidth) {
      const $dom = document.querySelector(`#${IDPREFIX}${self.uuid}`)
      if ($dom) {
        return $dom.clientWidth
      }
      return 'auto'
    } else {
      return self.width
    }
  },
  get heightCSS() {
    if (self.autoLayout?.autoHeight) {
      const $dom = document.querySelector(`#${IDPREFIX}${self.uuid}`)
      if ($dom) {
        return $dom.clientHeight
      }
      return 'auto'
    } else {
      return self.height
    }
  }
})).actions(self => ({
  setIsProcessing(isProcessing: boolean) {
    self.isProcessing = isProcessing
  },
  setBorderRadius(borderRadius: number | number[]) {
    self.borderRadius = borderRadius
  },
  setClipContent(clipContent: boolean) {
    self.clipContent = clipContent
  },
  enableAutoLayout() {
    self.autoLayout = AutoLayoutModel.create()
  },
  disableAutoLayout() {
    self.autoLayout = undefined
  },
  initBackground() {
    self.background = BackgroundModel.create()
  },
  setLockRatio(lockRatio: boolean) {
    self.lockRatio = lockRatio
  },
  setSize(width: number, height: number) {
    self.width = width
    self.height = height
  },
  setWidth(width: number) {
    if (self.lockRatio) {
      self.height = width / self.width * self.height
    }
    self.width = width
  },
  setHeight(height: number) {
    if (self.lockRatio) {
      self.width = height / self.height * self.width
    }
    self.height = height
  },
  setName(name: string) {
    self.name = name
  },
  setX(x: number) {
    self.x = x
  },
  setY(y: number) {
    self.y = y
  },
  setPosition(x: number, y: number) {
    self.x = x
    self.y = y
  },
  setAngle(angle: number) {
    self.angle = angle
  },
  setLocked(locked: boolean) {
    self.locked = locked
  },
  setVisible(visible: boolean) {
    self.visible = visible
  },
}))