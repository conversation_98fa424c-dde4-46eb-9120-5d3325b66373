import React, { useLayoutEffect, useState } from 'react'
import { observer } from 'mobx-react'
import {
	Frame,
	NodeText,
	NodeImage,
	NodePrice,
	NodeMedia,
	NodeTable,
	NodeAnchor,
} from './layers'
import { Carousel } from './common'
import { renderStore } from './store'
import {
	ILayerModel,
	isText,
	isImage,
	isGoods,
	isMedia,
	isTable,
	isPrice,
	ITextModel,
	isAnchor,
	ISchemaModel,
	isFrame,
} from './models'
import { IDPREFIX, LayerType } from './const'

interface IProps {
	model: ILayerModel
	index: number
	id?: string
	showRemark?: boolean
}

export const Layer: React.FC<IProps> = observer(
	({ model, id, showRemark, index }) => {
		return (
			<Frame
				model={model}
				index={index}
				className={`oly-render-layer oly-render-frame--${
					LayerType[model.type]
				}`}
				// style={{
				// 	width: model.autoSize ? 'fit-content' : model.width,
				// 	height: model.autoSize ? 'fit-content' : model.height,
				// 	zIndex: index,
				// 	WebkitTransform: model.transformCSS,
				// 	transform: model.transformCSS,
				// 	borderRadius: model.borderRadiusCSS,
				// 	background: model.background?.enable ? model.background?.css : 'none',
				// 	border: model.border?.enable ? model.border?.css : 0,
				// 	padding: model.paddingCSS,
				// 	visibility:
				// 		model.visible && !(model as ITextModel).editing
				// 			? 'visible'
				// 			: 'hidden',
				// 	overflow: model.clipContent ? 'hidden' : 'visible',
				// }}
				id={`${IDPREFIX}${id}`}
			>
				{/* <div className='oly-render-layer__wrapper'> */}
				{/* 文字图 */}
				{isText(model) ? (
					model.motion && model.motion?.type !== 'none' ? (
						<Carousel
							paused={renderStore.motionPaused}
							model={model}
						></Carousel>
					) : (
						// <FrameText model={model} index={index}></FrameText>
						<NodeText model={model}></NodeText>
					)
				) : null}
				{/* 位图&商品图 */}
				{(isImage(model) || isGoods(model)) && (
					// <FrameImage model={model} index={index}></FrameImage>
					<NodeImage model={model}></NodeImage>
				)}
				{/* 价格 */}
				{isPrice(model) && <NodePrice model={model} index={index}></NodePrice>}
				{/* 媒体 */}
				{isMedia(model) && (
					// <FrameMedia
					// 	model={model}
					// 	paused={renderStore.motionPaused}
					// ></FrameMedia>
					<NodeMedia
						model={model}
						paused={renderStore.motionPaused}
					></NodeMedia>
				)}
				{/* 表格 */}
				{isTable(model) && <NodeTable model={model} index={index}></NodeTable>}
				{/* 锚点标签 */}
				{isAnchor(model) && (
					<NodeAnchor model={model} index={index}></NodeAnchor>
				)}
				{/* 容器 */}
				{isFrame(model) &&
					model.children?.map((child, i) => (
						<Layer
							key={child.uuid}
							model={child}
							index={i}
							id={child.uuid}
						></Layer>
					))}
				{/* </div> */}

				{showRemark && (
					<div
						className='oly-render-remark'
						style={{
							width: model.widthCSS,
							height: model.heightCSS,
						}}
					>
						<div className='oly-render-remark__tag'>
							{model.name}
							<br />
							{model.type === 1 && `${model.widthCSS}x${model.heightCSS}`}
						</div>
					</div>
				)}
			</Frame>
		)
	}
)

// export const LayerTreeNode: React.FC<IProps> = observer(
// 	({ model, id, showRemark, index }) => {
// 		return (

// 		)
// 	})
