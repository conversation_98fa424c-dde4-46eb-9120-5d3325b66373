/*
 * @Description: 
 * @Author: hzweixin <EMAIL>
 * @Date: 2023-03-29 15:36:38
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-14 15:28:35
 */
// 
export const BaseFont = 'PingFangSC-Regular, PingFang SC, PingFangSC, PingFang, Helvetica, Arial, sans-serif'

// 元素类型枚举
export enum LayerType {
	Text, // 文本
	Image, // 图片
	Price, // 价格组，历史遗留问题，实际上是文本
	Vector, // 图形
	Goods, // 商品
	Frame, // 容器
	Media, // 媒体
	Table, // 表格
	Anchor, // 锚点
}

// 图层类型
export enum LayerTypeName {
	'文本',
	'图片',
	'价格',
	'图形',
	'商品图',
	'容器',
	'动图',
	'表格',
	'锚点标签',
}

export enum MotionType {
	None = 'none',
	Normal = 'normal',
	Carousel = 'carousel',
}

export enum MotionTypeName {
	'无',
	'普通',
	'轮播',
}

export enum DominantColorType {
	Common = 'common',
	Dark = 'dark',
	Light = 'light',
}

export enum ImageFitType {
	Fill = 'fill',
	Contain = 'contain',
	Cover = 'cover',
	ScaleDown = 'scale-down',
	None = 'none',
}

export enum ProcessingOp {
	Matting = 'matting', // 抠图
	MattingManual = 'matting_manual', // 手动抠图
	SmartCrop = 'smart_crop', // 智能裁剪
	AddShadow = 'add_shadow', // 添加阴影
	MaxPng = 'max_png', // 透底图最大化
}

export const IDPREFIX = 'layer-'

export const THEME_KEY = {
	key: 0,
	get newKey() {
		return ++this.key
	}
}

export enum AnchorDirection {
	Left = "left",
	Right = "right",
}

export enum AutoLayoutDirection {
	Row = "row",
	Column = "column",
}

export enum ColorType {
	Dark = 'dark',
	Light = 'light',
}

export enum GoodsImageType {
	Scene = 1,
	Transparent = 2,
}

export enum GoodsImageTypeName {
	'场景图' = 1,
	'透底图' = 2,
}

export enum AigcType {
	'智能' = '0',
	'营销卖点' = '1',
	'标题' = '2',
	'长文' = '3'
}