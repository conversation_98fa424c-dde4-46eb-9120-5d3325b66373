import './index.scss'
import React, { Ref, forwardRef } from 'react'
import { observer } from 'mobx-react'
import { Layer } from './Layer'
import { ILayerModel, ILayoutModel, ISchemaModel, ITraitModel } from './models'
import { LayerType } from './const'
import clsx from 'clsx'

interface IProps {
	schema: ISchemaModel
	ratio?: number // 缩放比例
	willRemark?: boolean // 是否可以展示预览状态下的标注框
	ssr?: boolean // 是否服务端渲染
	className?: string
	style?: React.CSSProperties
}

export const OlyRender = observer(
	forwardRef(
		(
			{ schema, ratio = 1, willRemark, ssr = false, className, style }: IProps,
			ref: Ref<HTMLDivElement>
		) => {
			const { layout, layers } = schema
			return (
				<div
					className={clsx('oly-render', className)}
					style={{
						...style,
						width: layout.width,
						height: layout.height,
						transform: `scale(${ratio})`,
					}}
					ref={ref}
				>
					{/* 背景 */}
					<div
						className='oly-render-background'
						style={{
							backgroundColor: layout.dominantColor?.enable
								? layout.dominantColor.value
								: layout.backgroundColor,
							backgroundImage: layout.backgroundImage,
						}}
					></div>
					<div className='oly-render-wrapper'>
						{layers.map((layer, index) => (
							<Layer
								index={layers.length - index}
								model={layer}
								key={layer.uuid}
								id={ssr ? undefined : layer.uuid}
								showRemark={
									willRemark &&
									layer.ext?.edit !== false &&
									layer.type !== LayerType.Price
								}
							></Layer>
						))}
						{/* {
						layerTree.map((layer, index) => {
							
						})
					} */}
					</div>
				</div>
			)
		}
	)
)
