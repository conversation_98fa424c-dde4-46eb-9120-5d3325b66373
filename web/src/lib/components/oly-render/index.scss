.oly-render {
  position: relative;
  overflow: hidden;
  transform-origin: 0 0;
  user-select: 'none';

  &-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: no-repeat 0/100%;

    &__image {
      background: no-repeat 0/100%;
    }
  }

  &-wrapper {
    overflow: hidden;
    user-select: none;
  }

  &-frame {
    box-sizing: border-box;

    &__wrapper {
      pointer-events: none;
    }

    .carousel-wrapper {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
    }

    &--Text,
    &--Anchor,
    &--Price {
      p {
        margin: 0;
        color: #000;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        letter-spacing: 0;
        line-height: inherit;
        padding: 0;
        text-align: left;
        text-transform: none;
        -webkit-font-smoothing: subpixel-antialiased;
        -moz-osx-font-smoothing: grayscale;
        vertical-align: middle;
        overflow-wrap: break-word;
        text-size-adjust: none;
        user-select: none;
        text-orientation: upright;
      }
    }

    &--Image,
    &--Media,
    &--Goods {
      img {
        position: absolute;
        pointer-events: none;
        width: 100%;
        height: 100%;
        object-fit: fill;
        border: 0;
      }

      video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background-color: 'transparent';
      }
    }

    &-info {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 2px solid rgb(44, 99, 255);
      z-index: 2;
      left: 0;
      top: 0;

      &__tag {
        position: absolute;
        left: 2px;
        top: 2px;
        font-size: 12px;
        color: #fff;
        background: var(--site-theme-color);
        border-radius: 4px;
        line-height: 1;
        padding: 4px 6px;
      }
    }
  }

  &-remark {
    position: absolute;
    border: 2px solid #2c63ff;
    z-index: 2;
    left: 0;
    top: 0;

    &__tag {
      position: absolute;
      left: -2px;
      top: -2px;
      font-size: 10px;
      color: #fff;
      background: #2c63ff;
      line-height: 1;
      padding: 2px 1px;
    }
  }
}