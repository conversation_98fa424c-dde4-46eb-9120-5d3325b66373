/* eslint-disable @typescript-eslint/no-var-requires */
const pkg = require('../../package.json');

export function replaceSource(source: { default: string }) {
    if (source && source.default) {
        // 替换lib路径
        // eslint-disable-next-line no-useless-escape
        const regExp = new RegExp(/[\'\"](\.\.\/)+lib[\'\"]/);
        return source.default.replace(regExp, `'${pkg.name}'`);
    }
    return '';
}
