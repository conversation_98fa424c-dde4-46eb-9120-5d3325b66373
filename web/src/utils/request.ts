import { AjaxResultCode, AjaxPaginationResult } from '@shark/core';

// 过滤http的code
export const filterCode = (res: AjaxPaginationResult<any>) => {
    // 登录
    if (res.code === AjaxResultCode.unauthorized) {
        logout();
    }
    return res;
};

export const logout = () => {
    window.location.href = `https://yx.mail.netease.com/openid/logout?url=${encodeURIComponent(
        window.location.href
    )}`;
};
