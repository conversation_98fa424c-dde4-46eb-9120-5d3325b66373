import React from 'react';
import { Divider } from 'antd';
import * as pkg from '../../../package.json';

export const Home: React.FC = () => (
    <>
        <section className="sharkr-section-global" key="global">
            <div className="sharkr-section-global-header">
                <span className="sharkr-section-global-header-title">{pkg.name}</span>
            </div>
            <div className="sharkr-section-global-content">
                <div className="paragraph-base" key="paragraph-base">
                    当前版本：
                    {pkg.version}
                </div>
                <p>包说明：包括使用场景、使用限制、node包信息等</p>
                <p>版本依赖：</p>
                <ul>
                    <li>antd^4.0.0</li>
                    <li>react^16.12.0</li>
                </ul>
            </div>
        </section>
        <section className="sharkr-section">
            <div className="sharkr-section-header">
                <span className="sharkr-section-header-title">日志</span>
            </div>
            <div className="sharkr-section-content" key="1">
                <div className="margin-b-base">
                    <a
                        href="http://yx.mail.netease.com/npm/#/packageManager/packageDetail;active=1;packageName=xxx"
                        rel="noopener noreferrer"
                        target="_blank">
                        详细历史
                    </a>
                </div>
                <div key="1">
                    <div className="title-base"> @1.1.0 (2019-07-20)</div>
                    <ul>
                        <li key="1">新增xxx</li>
                        <li key="2">修改xxx</li>
                        <li key="3">修复xxx</li>
                    </ul>
                </div>
                <Divider className="sharkr-section-divider" />
                <div key="0">
                    <div className="title-base"> @1.0.0 (2019-07-12)</div>
                    <ul>
                        <li key="1">新增xxx</li>
                        <li key="2">修改xxx</li>
                        <li key="3">修复xxx</li>
                    </ul>
                </div>
            </div>
        </section>
    </>
);
