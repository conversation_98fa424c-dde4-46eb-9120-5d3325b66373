import React from 'react'
import { Route, Redirect, Switch } from 'react-router-dom'
import { CorePage404 } from '@sharkr/components'
import { Home } from './home'
import { OlyRenderDoc } from './olyRender'
import { Demo1, Demo2, Demo3 } from './olyRender/demos'

export const AppRoute: React.FC = () => (
	<Switch>
		<Route component={Home} key='/home' path='/home' />
		{/* <Route component={OlyRenderDoc} key="/doc" path="/doc" /> */}
		<Route component={Demo1} key='/demo1' path='/demo1' />
		<Route component={Demo2} key='/demo2' path='/demo2' />
		<Route component={Demo3} key='/demo3' path='/demo3' />
		<Redirect exact from='/' to='/home' />
		<Route component={CorePage404} />
	</Switch>
)
