import React, { useState } from 'react'
import { Demo1, StrokeDemo, StrokeClipTest } from './demos'
import { Button } from 'antd'

export const OlyRenderDoc: React.FC = () => {
	const [currentDemo, setCurrentDemo] = useState<'demo1' | 'stroke' | 'clipTest'>('demo1')

	return (
		<div>
			<div style={{ marginBottom: 20, padding: 20, borderBottom: '1px solid #ddd' }}>
				<h2>Oly Render Demos</h2>
				<Button
					type={currentDemo === 'demo1' ? 'primary' : 'default'}
					onClick={() => setCurrentDemo('demo1')}
					style={{ marginRight: 10 }}
				>
					Demo 1
				</Button>
				<Button
					type={currentDemo === 'stroke' ? 'primary' : 'default'}
					onClick={() => setCurrentDemo('stroke')}
					style={{ marginRight: 10 }}
				>
					Text Stroke Demo
				</Button>
				<Button
					type={currentDemo === 'clipTest' ? 'primary' : 'default'}
					onClick={() => setCurrentDemo('clipTest')}
				>
					Clip Test
				</Button>
			</div>

			{currentDemo === 'demo1' && <Demo1 />}
			{currentDemo === 'stroke' && <StrokeDemo />}
			{currentDemo === 'clipTest' && <StrokeClipTest />}
		</div>
	)
}
