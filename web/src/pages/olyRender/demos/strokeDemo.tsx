import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lider, Input } from 'antd'
import React, { useState } from 'react'
import { SchemaModel, OlyRender, ITextModel, LayerType } from '../../../lib'
import { Observer } from 'mobx-react'
import React<PERSON><PERSON> from 'react-json-view'
import { getSnapshot } from 'mobx-state-tree'

// Create a simple schema with text for stroke testing
const createStrokeTestSchema = () => {
  const schema = SchemaModel.create({
    layout: {
      width: 800,
      height: 600,
      backgroundColor: '#f0f0f0'
    },
    layers: []
  })

  // Add a text layer for stroke testing
  const textLayer = schema.addLayer(LayerType.Text, {
    content: 'Text Stroke Demo',
    fontSize: 48,
    fontFamily: 'Arial',
    color: '#ffffff',
    x: 50,
    y: 100,
    width: 700,
    height: 80
  })

  // Add another text layer without stroke for comparison
  const normalTextLayer = schema.addLayer(LayerType.Text, {
    content: 'Normal Text (No Stroke)',
    fontSize: 32,
    fontFamily: 'Arial',
    color: '#333333',
    x: 50,
    y: 200,
    width: 700,
    height: 60
  })

  // Add a text layer with clipContent enabled to test stroke clipping
  const clippedTextLayer = schema.addLayer(LayerType.Text, {
    content: 'Clipped Container Test',
    fontSize: 36,
    fontFamily: 'Arial',
    color: '#ffffff',
    x: 50,
    y: 300,
    width: 400,
    height: 50,
    clipContent: true // This should not clip the stroke anymore
  })

  return schema
}

export const StrokeDemo = () => {
  const [schema] = useState(() => createStrokeTestSchema())
  const [strokeEnabled, setStrokeEnabled] = useState(false)
  const [strokeWidth, setStrokeWidth] = useState(2)
  const [strokeColor, setStrokeColor] = useState('#000000')

  const [clippedStrokeEnabled, setClippedStrokeEnabled] = useState(false)

  // Get the text layers for stroke manipulation
  const textLayer = schema.layers[0] as ITextModel
  const clippedTextLayer = schema.layers[2] as ITextModel

  const handleToggleStroke = () => {
    const newEnabled = !strokeEnabled
    setStrokeEnabled(newEnabled)
    textLayer.toggleStroke(newEnabled)
  }

  const handleStrokeWidthChange = (value: number) => {
    setStrokeWidth(value)
    textLayer.setStrokeWidth(value)
  }

  const handleStrokeColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const colorValue = e.target.value
    setStrokeColor(colorValue)
    textLayer.setStrokeColor(colorValue)
  }

  const handleToggleClippedStroke = () => {
    const newEnabled = !clippedStrokeEnabled
    setClippedStrokeEnabled(newEnabled)
    clippedTextLayer.toggleStroke(newEnabled)
    if (newEnabled) {
      clippedTextLayer.setStrokeWidth(strokeWidth)
      clippedTextLayer.setStrokeColor(strokeColor)
    }
  }

  return (
    <Observer>
      {() => (
        <div style={{ display: 'flex' }}>
          <div style={{ width: 600 }}>
            <div style={{ marginBottom: 20 }}>
              <h3>Text Stroke Controls</h3>
              <div style={{ marginBottom: 10 }}>
                <Button 
                  type={strokeEnabled ? 'primary' : 'default'}
                  onClick={handleToggleStroke}
                >
                  {strokeEnabled ? 'Disable Stroke' : 'Enable Stroke'}
                </Button>
              </div>
              
              {strokeEnabled && (
                <>
                  <div style={{ marginBottom: 10 }}>
                    <label>Stroke Width: {strokeWidth}px</label>
                    <Slider
                      min={1}
                      max={10}
                      value={strokeWidth}
                      onChange={handleStrokeWidthChange}
                      style={{ width: 200, marginLeft: 10 }}
                    />
                  </div>
                  
                  <div style={{ marginBottom: 10 }}>
                    <label>Stroke Color: </label>
                    <Input
                      type="color"
                      value={strokeColor}
                      onChange={handleStrokeColorChange}
                      style={{ width: 60, marginLeft: 10 }}
                    />
                  </div>
                </>
              )}

              <Divider />

              <div style={{ marginBottom: 10 }}>
                <h4>Clipped Container Test</h4>
                <p style={{ fontSize: '12px', color: '#666', marginBottom: 10 }}>
                  The third text has clipContent=true. Stroke should not be clipped with our fix.
                </p>
                <Button
                  type={clippedStrokeEnabled ? 'primary' : 'default'}
                  onClick={handleToggleClippedStroke}
                >
                  {clippedStrokeEnabled ? 'Disable' : 'Enable'} Stroke on Clipped Text
                </Button>
              </div>
            </div>
            
            <Divider />
            
            <div style={{ border: '1px solid #ddd', borderRadius: 4 }}>
              <OlyRender schema={schema} ratio={0.8} />
            </div>
          </div>
          
          <div style={{ flex: 1, marginLeft: 20 }}>
            <ReactJson
              name="schema"
              src={getSnapshot(schema)}
              style={{
                height: '100vh',
                overflowY: 'auto',
                fontFamily: 'Source Code Pro',
              }}
              displayDataTypes={false}
              iconStyle="square"
              collapsed={2}
            />
          </div>
        </div>
      )}
    </Observer>
  )
}
