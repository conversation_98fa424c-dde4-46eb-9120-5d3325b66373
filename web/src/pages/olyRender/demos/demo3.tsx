import { Button, Divider } from 'antd'
import React from 'react'
import { SchemaModel, OlyRender, ISchemaSnapshotOut } from '../../../lib'
import schema from './schema/schema3.json'
import ReactJson from 'react-json-view'
import { applySnapshot, getSnapshot } from 'mobx-state-tree'
import { PRESET_PRICE } from '../../../lib/components/oly-render/snippet'
import { observer } from 'mobx-react'

const schemaState = SchemaModel.create({})
applySnapshot(schemaState, schema as ISchemaSnapshotOut)


export const Demo3 = observer(() => (
	<div
		style={{
			display: 'flex',
		}}
	>
		<div
			style={{
				width: 600,
			}}
		>
			{/* <div>
				<Button
					onClick={() => {
						renderSchemaState.addLayer(0)
					}}
				>
					添加文字
				</Button>
				<Button
					onClick={() => {
						renderSchemaState.addLayer(1)
					}}
				>
					添加图片
				</Button>
				<Button
					onClick={() => {
						renderSchemaState.addLayer(2, PRESET_PRICE)
					}}
				>
					添加价格组
				</Button>
			</div>
			<div>
				<Button
					onClick={() => {
						renderStore.start()
					}}
				>
					开始
				</Button>
				<Button
					onClick={() => {
						renderStore.stop()
					}}
				>
					停止
				</Button>
				<Button
					onClick={() => {
						active?.motion?.addCarousel({
							text: `测试插入${Date.now()}`,
						})
						console.log(active?.motion?.contents.length)
					}}
				>
					插入一条
				</Button>
				<Button
					onClick={() => {
						// layerModelList.active?.motion?.addCarousel({
						// 	text: `${Date.now()}`,
						// })
						// console.log(layerModelList.active?.motion?.contents.length)
						const c = renderSchemaState.layers[0] as ITextModel
						c.initMotion()
					}}
				>
					初始化
				</Button>
			</div> */}
			<Divider></Divider>
			<OlyRender
				schema={schemaState}
				ratio={0.5}
			></OlyRender>
		</div>
		<ReactJson
			name='schema'
			src={getSnapshot(schemaState)}
			style={{
				height: '100vh',
				flex: 1,
				overflowY: 'auto',
				fontFamily: 'Source Code Pro',
			}}
			displayDataTypes={false}
			iconStyle='square'
		></ReactJson>
	</div>
))
