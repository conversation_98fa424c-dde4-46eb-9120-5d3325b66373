import { Button } from 'antd'
import React, { useState } from 'react'
import { SchemaModel, OlyRender, ITextModel, LayerType } from '../../../lib'
import { Observer } from 'mobx-react'

// Create a simple test schema to demonstrate stroke clipping fix
const createClipTestSchema = () => {
  const schema = SchemaModel.create({
    layout: {
      width: 800,
      height: 400,
      backgroundColor: '#f5f5f5'
    },
    layers: []
  })

  // Add text with clipContent=true (should NOT clip stroke with our fix)
  const clippedText = schema.addLayer(LayerType.Text, {
    content: 'Clipped Container',
    fontSize: 36,
    fontFamily: 'Arial',
    color: '#ffffff',
    x: 50,
    y: 50,
    width: 300,
    height: 50,
    clipContent: true // This is the key test - stroke should not be clipped
  })

  // Add text with clipContent=false for comparison
  const unclippedText = schema.addLayer(LayerType.Text, {
    content: 'Normal Container',
    fontSize: 36,
    fontFamily: 'Arial',
    color: '#ffffff',
    x: 50,
    y: 150,
    width: 300,
    height: 50,
    clipContent: false
  })

  // Add background rectangles to make the containers visible
  schema.addLayer(LayerType.Image, {
    content: 'data:image/svg+xml;base64,' + btoa(`
      <svg width="300" height="50" xmlns="http://www.w3.org/2000/svg">
        <rect width="300" height="50" fill="#333" stroke="#666" stroke-width="2"/>
      </svg>
    `),
    x: 50,
    y: 50,
    width: 300,
    height: 50,
    clipContent: false
  })

  schema.addLayer(LayerType.Image, {
    content: 'data:image/svg+xml;base64,' + btoa(`
      <svg width="300" height="50" xmlns="http://www.w3.org/2000/svg">
        <rect width="300" height="50" fill="#333" stroke="#666" stroke-width="2"/>
      </svg>
    `),
    x: 50,
    y: 150,
    width: 300,
    height: 50,
    clipContent: false
  })

  return schema
}

export const StrokeClipTest = () => {
  const [schema] = useState(() => createClipTestSchema())
  const [strokeEnabled, setStrokeEnabled] = useState(false)

  const clippedTextLayer = schema.layers[0] as ITextModel
  const normalTextLayer = schema.layers[1] as ITextModel

  const handleToggleStroke = () => {
    const newEnabled = !strokeEnabled
    setStrokeEnabled(newEnabled)
    
    // Apply stroke to both text layers
    clippedTextLayer.toggleStroke(newEnabled)
    normalTextLayer.toggleStroke(newEnabled)
    
    if (newEnabled) {
      // Set stroke properties
      clippedTextLayer.setStrokeWidth(3)
      clippedTextLayer.setStrokeColor('#ff0000')
      normalTextLayer.setStrokeWidth(3)
      normalTextLayer.setStrokeColor('#ff0000')
    }
  }

  return (
    <Observer>
      {() => (
        <div style={{ padding: 20 }}>
          <div style={{ marginBottom: 20 }}>
            <h3>Stroke Clipping Test</h3>
            <p>
              This test demonstrates that stroke is no longer clipped by <code>clipContent: true</code>.
              Both containers have the same stroke, but the top one has <code>clipContent: true</code>.
            </p>
            
            <Button 
              type={strokeEnabled ? 'primary' : 'default'}
              onClick={handleToggleStroke}
              size="large"
            >
              {strokeEnabled ? 'Disable' : 'Enable'} Red Stroke (3px)
            </Button>
          </div>
          
          <div style={{ 
            border: '1px solid #ddd', 
            borderRadius: 4,
            backgroundColor: '#fff',
            padding: 10
          }}>
            <OlyRender schema={schema} ratio={1} />
          </div>
          
          <div style={{ marginTop: 20, fontSize: '14px', color: '#666' }}>
            <p><strong>Top text:</strong> clipContent = true (stroke should NOT be clipped)</p>
            <p><strong>Bottom text:</strong> clipContent = false (normal behavior)</p>
            <p>If our fix works correctly, both texts should show the same stroke effect.</p>
          </div>
        </div>
      )}
    </Observer>
  )
}
