import React from 'react'
import { withRouter } from 'react-router-dom'
import { HomeOutlined } from '@ant-design/icons'
import { Menu, Badge, Input } from 'antd'
import { SharkRLayout, SharkRMenuProps } from '@sharkr/components'
import { getUserInfo } from '../../services'
import { logout } from '../../utils'
import { BaseLayoutProps } from './interface'

const { Header, Container, Sider, Content } = SharkRLayout
const { Left, Right, Menu: HeaderMenu, DropDown: HeaderDropDown } = Header
const { Menu: SiderMenu, Logo: SiderLogo } = Sider
const { Search } = Input

export const LayoutBase: React.FC<BaseLayoutProps> = (
	props: BaseLayoutProps
) => {
	const userMenu = (
		<Menu>
			<Menu.Item onClick={() => logout()}>退出</Menu.Item>
		</Menu>
	)

	return (
		<SharkRLayout mode='horizontal'>
			<Sider theme='dark'>
				<SiderLogo desc='' product={props.product || '严选B端模板'} />
				<SiderMenu {...(props.siderMenu as SharkRMenuProps)} theme='dark' />
			</Sider>
			<Container>
				<Header theme='light'>
					<Left>
						{props.headerMenu && (
							<Left>
								<HeaderMenu {...props.headerMenu} />
							</Left>
						)}
					</Left>
					<Right>
						<div className='sharkr-layout-header-tools'>
							<div className='tool'>
								<Search className='sharkr-w-md' />
							</div>
							<div className='tool'>
								<HomeOutlined className='sharkr-icon-left' />
								<a href=''>业务门户</a>
							</div>
							<div className='tool'>
								<HomeOutlined className='sharkr-icon-left' />
								<Badge count={5}>
									<a href=''>产品反馈</a>
								</Badge>
							</div>
							<div className='tool'>
								<HeaderDropDown overlay={userMenu} title={getUserInfo().name} />
							</div>
						</div>
					</Right>
				</Header>
				<Content className='body-transparent'>{props.children}</Content>
			</Container>
		</SharkRLayout>
	)
}

export const BaseLayout = withRouter((props: any) => <LayoutBase {...props} />)
