/* eslint-disable no-console */
import React from 'react'
import { HashRouter as Router } from 'react-router-dom'
import { SharkRMenuProps } from '@sharkr/components'
import { NotificationOutlined, LayoutOutlined } from '@ant-design/icons'
import { BaseLayout, BlankLayout } from './layouts'
import './app.scss'
import { AppRoute } from './appRoute'

export const App: React.FC = () => {
	const siderMenu: SharkRMenuProps = {
		data: [
			{
				name: 'README',
				icon: <NotificationOutlined />,
				link: {
					href: '#/home',
				},
			},
			// {
			//     name: '组件文档',
			//     icon: <LayoutOutlined />,
			//     link: {
			//         href: '#/doc'
			//     }
			// }
			{
				name: 'demo1-动图',
				link: {
					href: '#/demo1',
				},
			},
			{
				name: 'demo2-表格',
				link: {
					href: '#/demo2',
				},
			},
			{
				name: 'demo3-组',
				link: {
					href: '#/demo3',
				},
			},
		],
		afterViewInit: () => {
			console.log('afterViewInit')
		},
	}
	// 根据环境选择Layout（线上包需要在前端文档平台访问，不需要头部和侧边栏）
	const CLayout =
		process.env.NODE_ENV === 'production' ? BlankLayout : BaseLayout
	const props = process.env.NODE_ENV === 'production' ? {} : { siderMenu }
	return (
		<Router>
			<CLayout {...props}>
				<AppRoute />
			</CLayout>
		</Router>
	)
}
