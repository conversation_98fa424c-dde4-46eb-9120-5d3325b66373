import React from 'react';
import ReactDOM from 'react-dom';
import { AppConfig } from '@shark/core';
import './index.scss';
import { App } from './pages/app';
import * as serviceWorker from './serviceWorker';

AppConfig.configure({
    contextPath: '/oly-render',
    productCode: 'ops'
});

ReactDOM.render(<App />, document.getElementById('root'));

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
